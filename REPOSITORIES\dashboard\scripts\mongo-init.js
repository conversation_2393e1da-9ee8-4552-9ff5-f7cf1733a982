// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the exlog database
db = db.getSiblingDB('exlog');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email', 'password', 'firstName', 'lastName', 'role'],
      properties: {
        username: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 50,
          description: 'Username must be a string between 3 and 50 characters'
        },
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          description: 'Email must be a valid email address'
        },
        role: {
          bsonType: 'string',
          enum: ['admin', 'security_analyst', 'compliance_officer', 'executive'],
          description: 'Role must be one of the predefined values'
        },
        status: {
          bsonType: 'string',
          enum: ['active', 'inactive', 'locked'],
          description: 'Status must be one of the predefined values'
        }
      }
    }
  }
});

db.createCollection('logs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['logId', 'timestamp', 'source', 'sourceType', 'host', 'logLevel', 'message'],
      properties: {
        logId: {
          bsonType: 'string',
          description: 'Log ID is required and must be a string'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Timestamp is required and must be a date'
        },
        source: {
          bsonType: 'string',
          enum: ['System', 'Application', 'Security', 'Network', 'Custom'],
          description: 'Source must be one of the predefined values'
        },
        sourceType: {
          bsonType: 'string',
          enum: ['event', 'application', 'security', 'network', 'audit', 'performance'],
          description: 'Source type must be one of the predefined values'
        },
        logLevel: {
          bsonType: 'string',
          enum: ['critical', 'error', 'warning', 'info', 'debug'],
          description: 'Log level must be one of the predefined values'
        }
      }
    }
  }
});

// Create indexes for better performance
db.users.createIndex({ username: 1 }, { unique: true });
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ status: 1 });

db.logs.createIndex({ logId: 1 }, { unique: true });
db.logs.createIndex({ timestamp: -1 });
db.logs.createIndex({ source: 1 });
db.logs.createIndex({ logLevel: 1 });
db.logs.createIndex({ host: 1 });
db.logs.createIndex({ 'metadata.agentId': 1 });
db.logs.createIndex({ timestamp: -1, source: 1 });
db.logs.createIndex({ timestamp: -1, logLevel: 1 });
db.logs.createIndex({ alertTriggered: 1, timestamp: -1 });
db.logs.createIndex({ archived: 1, timestamp: -1 });

// Text index for full-text search
db.logs.createIndex({
  message: 'text',
  'additionalFields.description': 'text',
  'additionalFields.details': 'text'
});

// Create default admin user (password: Admin123!)
// Note: In production, this should be changed immediately
db.users.insertOne({
  username: 'admin',
  email: '<EMAIL>',
  password: '$2a$12$cr6szGDjXG7haljTZSxM7uMhahvHz/A6xqjYjSVWhKvEmsMYdws5G', // Your custom password
  firstName: 'System',
  lastName: 'Administrator',
  role: 'admin',
  permissions: [],
  mfaEnabled: false,
  status: 'active',
  loginAttempts: 0,
  preferences: {
    theme: 'light',
    timezone: 'UTC',
    language: 'en',
    notifications: {
      email: true,
      inApp: true,
      alerts: true
    }
  },
  createdAt: new Date(),
  updatedAt: new Date()
});

print('MongoDB initialization completed successfully');
print('Default admin user created: <EMAIL> / Your custom password');
print('Please change the default password after first login');

