{"version": 3, "file": "ColumnFormatter.js", "sourceRoot": "", "sources": ["../../../../src/parser/column/ColumnFormatter.ts"], "names": [], "mappings": ";;;AAEA,MAAa,eAAe;IACR,MAAM,CAA0B;IAEhD,YAAmB,aAA4B;QAC3C,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE;gBAClC,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;YACtB,CAAC,CAAC;QACN,CAAC;aAAM,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE;gBAClC,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC1B,CAAC,CAAC;QACN,CAAC;aAAM,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE;gBAClC,OAAO,GAAG,CAAC,SAAS,EAAE,CAAC;YAC3B,CAAC,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE;gBAClC,OAAO,GAAG,CAAC;YACf,CAAC,CAAC;QACN,CAAC;IACL,CAAC;CACJ;AAtBD,0CAsBC"}