import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Snackbar,
} from '@mui/material'
import {
  Download,
  Computer,
  Refresh,
  Settings,
  Delete,
  Note,
  CheckCircle,
  Warning,
  Error,
  OfflinePin,
  DesktopWindows,
  Storage,
  Laptop,
} from '@mui/icons-material'
import { fetchAgents, fetchAgentStats, downloadAgent, updateAgent, addAgentNote, deleteAgent } from '../../store/slices/agentsSlice'

const Agents = () => {
  const dispatch = useDispatch()
  const { agents, stats, pagination, isLoading, error } = useSelector((state) => state.agents)

  const [activeTab, setActiveTab] = useState(0)
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [noteDialogOpen, setNoteDialogOpen] = useState(false)
  const [noteContent, setNoteContent] = useState('')
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })
  const [filters, setFilters] = useState({
    status: '',
    platform: '',
    search: ''
  })

  useEffect(() => {
    dispatch(fetchAgents({ page: 1, limit: 20 }))
    dispatch(fetchAgentStats())
  }, [dispatch])

  const handleRefresh = () => {
    dispatch(fetchAgents({ page: 1, limit: 20, ...filters }))
    dispatch(fetchAgentStats())
  }

  const handleDownloadAgent = async () => {
    try {
      await dispatch(downloadAgent('windows')).unwrap()
      setSnackbar({
        open: true,
        message: 'Agent download started',
        severity: 'success'
      })
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || 'Download failed',
        severity: 'error'
      })
    }
  }

  const handleEditAgent = (agent) => {
    setSelectedAgent(agent)
    setEditDialogOpen(true)
  }

  const handleAddNote = (agent) => {
    setSelectedAgent(agent)
    setNoteContent('')
    setNoteDialogOpen(true)
  }

  const handleSaveNote = async () => {
    if (!noteContent.trim()) return

    try {
      await dispatch(addAgentNote({
        id: selectedAgent._id,
        content: noteContent
      })).unwrap()

      setNoteDialogOpen(false)
      setNoteContent('')
      setSnackbar({
        open: true,
        message: 'Note added successfully',
        severity: 'success'
      })
      handleRefresh()
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to add note',
        severity: 'error'
      })
    }
  }

  const handleDeleteAgent = async (agent) => {
    if (!window.confirm(`Are you sure you want to deactivate agent ${agent.hostname}?`)) {
      return
    }

    try {
      await dispatch(deleteAgent(agent._id)).unwrap()
      setSnackbar({
        open: true,
        message: 'Agent deactivated successfully',
        severity: 'success'
      })
      handleRefresh()
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to deactivate agent',
        severity: 'error'
      })
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online':
        return <CheckCircle color="success" />
      case 'warning':
        return <Warning color="warning" />
      case 'error':
        return <Error color="error" />
      default:
        return <OfflinePin color="disabled" />
    }
  }

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'windows':
        return <DesktopWindows />
      case 'linux':
        return <Storage />
      case 'macos':
        return <Laptop />
      default:
        return <Computer />
    }
  }

  const formatLastSeen = (date) => {
    const now = new Date()
    const lastSeen = new Date(date)
    const diffMinutes = Math.floor((now - lastSeen) / (1000 * 60))

    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
    return `${Math.floor(diffMinutes / 1440)}d ago`
  }

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  )

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Agent Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage log collection agents across your infrastructure.
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={isLoading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Download />}
            onClick={handleDownloadAgent}
          >
            Download Agent
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Agents
                  </Typography>
                  <Typography variant="h4">
                    {stats?.summary?.total || 0}
                  </Typography>
                </Box>
                <Computer sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Online
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {stats?.summary?.online || 0}
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Warning
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {stats?.summary?.warning || 0}
                  </Typography>
                </Box>
                <Warning sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Offline
                  </Typography>
                  <Typography variant="h4" color="error.main">
                    {stats?.summary?.offline || 0}
                  </Typography>
                </Box>
                <OfflinePin sx={{ fontSize: 40, color: 'error.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Card>
        <CardContent>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
            <Tab label="All Agents" />
            <Tab label="Download" />
          </Tabs>

          <TabPanel value={activeTab} index={0}>
            {isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : agents.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <Computer sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No Agents Found
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  Deploy agents to start collecting logs from your infrastructure.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Download />}
                  onClick={() => setActiveTab(1)}
                >
                  Download Agent
                </Button>
              </Box>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Status</TableCell>
                      <TableCell>Hostname</TableCell>
                      <TableCell>Platform</TableCell>
                      <TableCell>Version</TableCell>
                      <TableCell>Last Seen</TableCell>
                      <TableCell>IP Address</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {agents.map((agent) => (
                      <TableRow key={agent._id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getStatusIcon(agent.status)}
                            <Chip
                              label={agent.status}
                              size="small"
                              color={
                                agent.status === 'online' ? 'success' :
                                agent.status === 'warning' ? 'warning' :
                                agent.status === 'error' ? 'error' : 'default'
                              }
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {agent.hostname}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {agent.agentId}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getPlatformIcon(agent.platform)}
                            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                              {agent.platform}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {agent.version}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatLastSeen(agent.lastSeen)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {agent.ipAddress || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Edit Agent">
                              <IconButton
                                size="small"
                                onClick={() => handleEditAgent(agent)}
                              >
                                <Settings />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Add Note">
                              <IconButton
                                size="small"
                                onClick={() => handleAddNote(agent)}
                              >
                                <Note />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Deactivate Agent">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteAgent(agent)}
                              >
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            <Box sx={{ maxWidth: 600, mx: 'auto', textAlign: 'center' }}>
              <Computer sx={{ fontSize: 80, color: 'primary.main', mb: 3 }} />
              <Typography variant="h5" gutterBottom>
                Download ExLog Agent
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Deploy the ExLog agent on your systems to start collecting and forwarding logs
                to your dashboard. The agent is lightweight and runs as a system service.
              </Typography>

              <Card sx={{ mb: 4, textAlign: 'left' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Windows Agent
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Compatible with Windows Server 2016+ and Windows 10+
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <DesktopWindows sx={{ color: 'primary.main' }} />
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        ExLog Agent v2.0.0
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        MSI Installer • ~15 MB
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      startIcon={<Download />}
                      onClick={handleDownloadAgent}
                    >
                      Download
                    </Button>
                  </Box>
                </CardContent>
              </Card>

              <Alert severity="info" sx={{ textAlign: 'left' }}>
                <Typography variant="body2" fontWeight="medium" gutterBottom>
                  Installation Instructions:
                </Typography>
                <Typography variant="body2" component="div">
                  1. Download and run the MSI installer as Administrator<br />
                  2. The agent will automatically register with this dashboard<br />
                  3. Check the "All Agents" tab to verify successful registration<br />
                  4. Logs will start appearing in the dashboard within minutes
                </Typography>
              </Alert>

              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Coming Soon:</strong> Linux and macOS agents
                </Typography>
              </Box>
            </Box>
          </TabPanel>
        </CardContent>
      </Card>

      {/* Edit Agent Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Agent</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Tags (comma separated)"
              value={selectedAgent?.tags?.join(', ') || ''}
              onChange={(e) => setSelectedAgent({
                ...selectedAgent,
                tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
              })}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={selectedAgent?.alertsEnabled || false}
                  onChange={(e) => setSelectedAgent({
                    ...selectedAgent,
                    alertsEnabled: e.target.checked
                  })}
                />
              }
              label="Enable Alerts"
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={selectedAgent?.maintenanceMode || false}
                  onChange={(e) => setSelectedAgent({
                    ...selectedAgent,
                    maintenanceMode: e.target.checked
                  })}
                />
              }
              label="Maintenance Mode"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => {
            // Handle save
            setEditDialogOpen(false)
          }}>
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Note Dialog */}
      <Dialog open={noteDialogOpen} onClose={() => setNoteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Note</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Note"
            value={noteContent}
            onChange={(e) => setNoteContent(e.target.value)}
            placeholder="Add a note about this agent..."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNoteDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleSaveNote} disabled={!noteContent.trim()}>
            Add Note
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default Agents
