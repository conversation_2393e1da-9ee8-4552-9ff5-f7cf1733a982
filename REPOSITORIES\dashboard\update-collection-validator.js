// Script to update MongoDB collection validator with new source types
db = db.getSiblingDB('exlog');

// Update the logs collection validator to include new source types
const result = db.runCommand({
  collMod: "logs",
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['logId', 'timestamp', 'source', 'sourceType', 'host', 'logLevel', 'message'],
      properties: {
        logId: {
          bsonType: 'string',
          description: 'Log ID is required and must be a string'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Timestamp is required and must be a date'
        },
        source: {
          bsonType: 'string',
          enum: ['System', 'Application', 'Security', 'Network', 'Custom', 'Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd'],
          description: 'Source must be one of the predefined values'
        },
        sourceType: {
          bsonType: 'string',
          enum: ['event', 'application', 'security', 'network', 'audit', 'performance', 'auth', 'kernel', 'service', 'scheduler', 'hardware', 'systemd'],
          description: 'Source type must be one of the predefined values'
        },
        osType: {
          bsonType: 'string',
          enum: ['windows', 'linux'],
          description: 'OS type must be windows or linux'
        },
        host: {
          bsonType: 'string',
          description: 'Host is required and must be a string'
        },
        logLevel: {
          bsonType: 'string',
          enum: ['critical', 'error', 'warning', 'info', 'debug'],
          description: 'Log level must be one of the predefined values'
        },
        message: {
          bsonType: 'string',
          description: 'Message is required and must be a string'
        }
      }
    }
  }
});

print('Collection validator updated:', JSON.stringify(result));

// Verify the validator was updated
const collectionInfo = db.runCommand({listCollections: 1, filter: {name: "logs"}});
print('Updated collection info:', JSON.stringify(collectionInfo.cursor.firstBatch[0].options.validator, null, 2));
