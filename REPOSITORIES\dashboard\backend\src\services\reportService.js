const puppeteer = require('puppeteer');
const PDFDocument = require('pdfkit');
const csv = require('fast-csv');
const fs = require('fs').promises;
const path = require('path');
const Log = require('../models/Log');
const Alert = require('../models/Alert');
const User = require('../models/User');
const logger = require('../config/logger');

class ReportService {
  constructor() {
    this.browser = null;
  }

  async initBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    }
    return this.browser;
  }

  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  // Execute Analytics Report
  async executeAnalyticsReport(report, parameters = {}) {
    const { timeRange = '30d', filters = {} } = parameters;
    const startDate = this.getStartDate(timeRange);
    
    try {
      const data = await this.getAnalyticsData(report.content, startDate, filters);
      return {
        type: 'analytics',
        data,
        metadata: {
          timeRange,
          generatedAt: new Date(),
          recordCount: data.totalRecords || 0
        }
      };
    } catch (error) {
      logger.error('Analytics report execution failed:', error);
      throw error;
    }
  }

  // Execute Compliance Report
  async executeComplianceReport(report, parameters = {}) {
    const { framework = 'pci', timeRange = '30d' } = parameters;
    const startDate = this.getStartDate(timeRange);

    try {
      const complianceData = await this.getComplianceData(framework, startDate);
      return {
        type: 'compliance',
        framework,
        data: complianceData,
        metadata: {
          timeRange,
          generatedAt: new Date(),
          assessmentDate: new Date()
        }
      };
    } catch (error) {
      logger.error('Compliance report execution failed:', error);
      throw error;
    }
  }

  // Execute Custom Report
  async executeCustomReport(report, parameters = {}) {
    const { filters = {}, timeRange = '30d' } = parameters;
    const startDate = this.getStartDate(timeRange);

    try {
      const data = await this.getCustomReportData(report.content, startDate, filters);
      return {
        type: 'custom',
        data,
        metadata: {
          timeRange,
          generatedAt: new Date(),
          components: report.content.components || []
        }
      };
    } catch (error) {
      logger.error('Custom report execution failed:', error);
      throw error;
    }
  }

  // Get Analytics Data
  async getAnalyticsData(content, startDate, filters) {
    const query = { timestamp: { $gte: startDate } };

    // Apply filters - fix field name inconsistency
    if (filters.logLevel) query.logLevel = filters.logLevel;
    if (filters.source) query.source = filters.source;
    if (filters.host) query.host = filters.host;

    const [
      totalLogs,
      logsByLevel,
      logsBySource,
      logsByHour,
      logsByDay,
      topHosts,
      recentAlerts,
      alertsByStatus,
      alertsBySeverity,
      logTrend,
      securityEvents,
      systemPerformance
    ] = await Promise.all([
      Log.countDocuments(query),
      this.getLogsByLevel(query),
      this.getLogsBySource(query),
      this.getLogsByHour(query),
      this.getLogsByDay(query),
      this.getTopHosts(query),
      this.getRecentAlerts(startDate),
      this.getAlertsByStatus(startDate),
      this.getAlertsBySeverity(startDate),
      this.getLogTrend(query),
      this.getSecurityEvents(query),
      this.getSystemPerformance(query)
    ]);

    // Calculate comprehensive summary metrics
    const summary = {
      totalLogs,
      criticalEvents: logsByLevel.find(l => l._id === 'critical')?.count || 0,
      errorEvents: logsByLevel.find(l => l._id === 'error')?.count || 0,
      warningEvents: logsByLevel.find(l => l._id === 'warning')?.count || 0,
      infoEvents: logsByLevel.find(l => l._id === 'info')?.count || 0,
      debugEvents: logsByLevel.find(l => l._id === 'debug')?.count || 0,
      uniqueHosts: topHosts.length,
      uniqueSources: logsBySource.length,
      totalAlerts: alertsByStatus.reduce((sum, alert) => sum + alert.count, 0),
      activeAlerts: alertsByStatus.filter(a => ['new', 'acknowledged', 'investigating'].includes(a._id)).reduce((sum, alert) => sum + alert.count, 0),
      resolvedAlerts: alertsByStatus.find(a => a._id === 'resolved')?.count || 0
    };

    return {
      totalRecords: totalLogs,
      logsByLevel,
      logsBySource,
      logsByHour,
      logsByDay,
      topHosts,
      recentAlerts,
      alertsByStatus,
      alertsBySeverity,
      logTrend,
      securityEvents,
      systemPerformance,
      summary
    };
  }

  // Get Compliance Data
  async getComplianceData(framework, startDate) {
    const query = { timestamp: { $gte: startDate } };

    // Framework-specific compliance checks
    const complianceRules = this.getComplianceRules(framework);
    const results = {};

    // Get additional compliance metrics
    const [
      authenticationEvents,
      accessControlEvents,
      dataProtectionEvents,
      auditEvents,
      configurationChanges,
      failedLogins,
      privilegeEscalations
    ] = await Promise.all([
      this.getAuthenticationEvents(query),
      this.getAccessControlEvents(query),
      this.getDataProtectionEvents(query),
      this.getAuditEvents(query),
      this.getConfigurationChanges(query),
      this.getFailedLogins(query),
      this.getPrivilegeEscalations(query)
    ]);

    // Evaluate each compliance rule
    for (const rule of complianceRules) {
      results[rule.id] = await this.evaluateComplianceRule(rule, query, {
        authenticationEvents,
        accessControlEvents,
        dataProtectionEvents,
        auditEvents,
        configurationChanges,
        failedLogins,
        privilegeEscalations
      });
    }

    const totalRules = complianceRules.length;
    const passedRules = Object.values(results).filter(r => r.status === 'pass').length;
    const complianceScore = Math.round((passedRules / totalRules) * 100);

    return {
      framework,
      complianceScore,
      totalRules,
      passedRules,
      failedRules: totalRules - passedRules,
      results,
      recommendations: this.getComplianceRecommendations(results),
      metrics: {
        authenticationEvents: authenticationEvents.length,
        accessControlEvents: accessControlEvents.length,
        dataProtectionEvents: dataProtectionEvents.length,
        auditEvents: auditEvents.length,
        configurationChanges: configurationChanges.length,
        failedLogins: failedLogins.length,
        privilegeEscalations: privilegeEscalations.length
      },
      details: {
        authenticationEvents,
        accessControlEvents,
        dataProtectionEvents,
        auditEvents,
        configurationChanges,
        failedLogins,
        privilegeEscalations
      }
    };
  }

  // Get Custom Report Data
  async getCustomReportData(content, startDate, filters) {
    const { components = [], dataSourceConfig = {} } = content;
    const data = {};

    for (const component of components) {
      try {
        data[component.id] = await this.getComponentData(component, startDate, filters, dataSourceConfig);
      } catch (error) {
        logger.error(`Failed to get data for component ${component.id}:`, error);
        data[component.id] = { error: error.message };
      }
    }

    return data;
  }

  // Generate PDF Report
  async generatePDF(reportData, options = {}) {
    const { title = 'Report', format = 'A4' } = options;
    
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ size: format, margin: 50 });
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);

        // Add title
        doc.fontSize(20).text(title, { align: 'center' });
        doc.moveDown();

        // Add metadata
        doc.fontSize(12).text(`Generated: ${new Date().toLocaleString()}`, { align: 'right' });
        doc.moveDown();

        // Add content based on report type
        this.addPDFContent(doc, reportData);

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Generate CSV Report
  async generateCSV(reportData) {
    return new Promise((resolve, reject) => {
      const csvData = this.formatDataForCSV(reportData);
      const chunks = [];

      csv.writeToString(csvData, { headers: true })
        .on('data', chunk => chunks.push(chunk))
        .on('end', () => resolve(chunks.join('')))
        .on('error', reject);
    });
  }

  // Generate HTML Report for PDF conversion
  generateHTMLReport(reportData, options = {}) {
    const { title = 'Report', includeCharts = true } = options;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
          }
          .header h1 { margin: 0; font-size: 2.5em; }
          .header p { margin: 5px 0; opacity: 0.9; }
          .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
          }
          .section h3 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 15px;
          }
          .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
          }
          .summary-card {
            flex: 1;
            min-width: 150px;
            padding: 20px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
          }
          .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 0.9em;
            color: #657786;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 0;
            color: #2c3e50;
          }
          .summary-card.critical .metric-value { color: #e74c3c; }
          .summary-card.error .metric-value { color: #e67e22; }
          .summary-card.warning .metric-value { color: #f39c12; }
          .summary-card.info .metric-value { color: #3498db; }
          .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .data-table th, .data-table td {
            border: none;
            padding: 12px 15px;
            text-align: left;
          }
          .data-table th {
            background: #34495e;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
          }
          .data-table tr:nth-child(even) { background: #f8f9fa; }
          .data-table tr:hover { background: #e8f4f8; }
          .data-table td.critical { color: #e74c3c; font-weight: bold; }
          .data-table td.error { color: #e67e22; font-weight: bold; }
          .data-table td.warning { color: #f39c12; font-weight: bold; }
          .data-table td.info { color: #3498db; font-weight: bold; }
          .chart-container {
            margin: 25px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          @media print {
            body { margin: 0; }
            .section { break-inside: avoid; }
            .summary-cards { display: block; }
            .summary-card { display: inline-block; width: 30%; margin: 5px; }
          }
        </style>
      </head>
      <body>
        ${this.generateHTMLContent(reportData, title)}
      </body>
      </html>
    `;
  }

  // Helper Methods
  getStartDate(timeRange) {
    const now = new Date();
    switch (timeRange) {
      case '1h': return new Date(now - 60 * 60 * 1000);
      case '24h': return new Date(now - 24 * 60 * 60 * 1000);
      case '7d': return new Date(now - 7 * 24 * 60 * 60 * 1000);
      case '30d': return new Date(now - 30 * 24 * 60 * 60 * 1000);
      case '90d': return new Date(now - 90 * 24 * 60 * 60 * 1000);
      default: return new Date(now - 30 * 24 * 60 * 60 * 1000);
    }
  }

  async getLogsByLevel(query) {
    return Log.aggregate([
      { $match: query },
      { $group: { _id: '$logLevel', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
  }

  async getLogsBySource(query) {
    return Log.aggregate([
      { $match: query },
      { $group: { _id: '$source', count: { $sum: 1 }, latestTimestamp: { $max: '$timestamp' } } },
      { $sort: { count: -1 } },
      { $limit: 15 }
    ]);
  }

  async getLogsByHour(query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d %H:00', date: '$timestamp' } },
          count: { $sum: 1 },
          criticalCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'critical'] }, 1, 0] } },
          errorCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'error'] }, 1, 0] } },
          warningCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'warning'] }, 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);
  }

  async getLogsByDay(query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
          count: { $sum: 1 },
          criticalCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'critical'] }, 1, 0] } },
          errorCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'error'] }, 1, 0] } },
          warningCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'warning'] }, 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);
  }

  async getTopHosts(query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$host',
          count: { $sum: 1 },
          criticalCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'critical'] }, 1, 0] } },
          errorCount: { $sum: { $cond: [{ $eq: ['$logLevel', 'error'] }, 1, 0] } },
          latestActivity: { $max: '$timestamp' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 15 }
    ]);
  }

  async getRecentAlerts(startDate) {
    return Alert.find({
      triggeredAt: { $gte: startDate }
    })
    .populate('ruleId', 'name category')
    .sort({ triggeredAt: -1 })
    .limit(20)
    .lean();
  }

  async getAlertsByStatus(startDate) {
    return Alert.aggregate([
      { $match: { triggeredAt: { $gte: startDate } } },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
  }

  async getAlertsBySeverity(startDate) {
    return Alert.aggregate([
      { $match: { triggeredAt: { $gte: startDate } } },
      { $group: { _id: '$severity', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
  }

  async getLogTrend(query) {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)); // Last 7 days

    return Log.aggregate([
      { $match: { ...query, timestamp: { $gte: startDate, $lte: endDate } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
  }

  async getSecurityEvents(query) {
    return Log.aggregate([
      {
        $match: {
          ...query,
          $or: [
            { source: 'Security' },
            { sourceType: 'security' },
            { logLevel: { $in: ['critical', 'error'] } },
            { alertTriggered: true }
          ]
        }
      },
      {
        $group: {
          _id: {
            source: '$source',
            logLevel: '$logLevel'
          },
          count: { $sum: 1 },
          latestTimestamp: { $max: '$timestamp' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
  }

  async getSystemPerformance(query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$host',
          totalLogs: { $sum: 1 },
          errorRate: {
            $avg: {
              $cond: [
                { $in: ['$logLevel', ['critical', 'error']] },
                1,
                0
              ]
            }
          },
          avgProcessingTime: { $avg: '$metadata.processingTime' },
          lastActivity: { $max: '$timestamp' }
        }
      },
      { $sort: { totalLogs: -1 } },
      { $limit: 10 }
    ]);
  }

  getComplianceRules(framework) {
    const rules = {
      pci: [
        { id: 'pci_1', name: 'Install and maintain firewall configuration', category: 'Network Security' },
        { id: 'pci_2', name: 'Do not use vendor-supplied defaults', category: 'System Configuration' },
        { id: 'pci_3', name: 'Protect stored cardholder data', category: 'Data Protection' },
        { id: 'pci_4', name: 'Encrypt transmission of cardholder data', category: 'Data Transmission' }
      ],
      hipaa: [
        { id: 'hipaa_1', name: 'Access Control', category: 'Administrative Safeguards' },
        { id: 'hipaa_2', name: 'Audit Controls', category: 'Technical Safeguards' },
        { id: 'hipaa_3', name: 'Integrity', category: 'Technical Safeguards' },
        { id: 'hipaa_4', name: 'Person or Entity Authentication', category: 'Technical Safeguards' }
      ],
      soc2: [
        { id: 'soc2_1', name: 'Security', category: 'Trust Services Criteria' },
        { id: 'soc2_2', name: 'Availability', category: 'Trust Services Criteria' },
        { id: 'soc2_3', name: 'Processing Integrity', category: 'Trust Services Criteria' },
        { id: 'soc2_4', name: 'Confidentiality', category: 'Trust Services Criteria' }
      ]
    };
    return rules[framework] || rules.pci;
  }

  async evaluateComplianceRule(rule, query, complianceData = {}) {
    let relevantLogs = 0;
    let status = 'fail';
    let score = 0;
    let details = '';
    let evidence = [];

    // Enhanced compliance evaluation based on rule type and framework
    switch (rule.id) {
      case 'pci_1': // Firewall configuration
        relevantLogs = await Log.countDocuments({
          ...query,
          $or: [
            { message: { $regex: 'firewall|iptables|ufw', $options: 'i' } },
            { source: 'Network' },
            { 'additionalFields.event_type': 'network_security' }
          ]
        });
        status = relevantLogs > 10 ? 'pass' : 'fail';
        break;

      case 'pci_2': // Default passwords
        relevantLogs = await Log.countDocuments({
          ...query,
          $or: [
            { message: { $regex: 'password.*change|credential.*update', $options: 'i' } },
            { 'additionalFields.event_type': 'password_change' }
          ]
        });
        status = relevantLogs > 0 ? 'pass' : 'fail';
        break;

      case 'hipaa_1': // Access Control
        relevantLogs = complianceData.accessControlEvents?.length || 0;
        status = relevantLogs > 5 ? 'pass' : 'fail';
        break;

      case 'hipaa_2': // Audit Controls
        relevantLogs = complianceData.auditEvents?.length || 0;
        status = relevantLogs > 0 ? 'pass' : 'fail';
        break;

      default:
        // Generic evaluation
        relevantLogs = await Log.countDocuments({
          ...query,
          $or: [
            { message: { $regex: rule.name, $options: 'i' } },
            { 'additionalFields.event_category': rule.category }
          ]
        });
        status = relevantLogs > 0 ? 'pass' : 'fail';
    }

    score = status === 'pass' ? 100 : 0;
    details = `Found ${relevantLogs} related log entries for ${rule.name}`;
    evidence = relevantLogs;

    return {
      status,
      score,
      evidence,
      lastChecked: new Date(),
      details,
      ruleCategory: rule.category,
      ruleName: rule.name
    };
  }

  // Compliance-specific data gathering methods
  async getAuthenticationEvents(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'login|authentication|logon', $options: 'i' } },
        { sourceType: 'security' },
        { 'additionalFields.event_type': 'authentication' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getAccessControlEvents(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'access.*denied|permission.*denied|unauthorized', $options: 'i' } },
        { 'additionalFields.event_type': 'access_control' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getDataProtectionEvents(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'encryption|decrypt|data.*protection', $options: 'i' } },
        { 'additionalFields.event_type': 'data_protection' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getAuditEvents(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'audit|log.*access|file.*access', $options: 'i' } },
        { sourceType: 'audit' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getConfigurationChanges(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'config.*change|setting.*modified|policy.*update', $options: 'i' } },
        { 'additionalFields.event_type': 'configuration_change' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getFailedLogins(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'login.*failed|authentication.*failed|logon.*failed', $options: 'i' } },
        { logLevel: { $in: ['error', 'warning'] }, message: { $regex: 'login|authentication', $options: 'i' } }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  async getPrivilegeEscalations(query) {
    return Log.find({
      ...query,
      $or: [
        { message: { $regex: 'privilege.*escalation|sudo|admin.*access|elevated.*permission', $options: 'i' } },
        { 'additionalFields.event_type': 'privilege_escalation' }
      ]
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
  }

  getComplianceRecommendations(results) {
    const recommendations = [];

    Object.entries(results).forEach(([ruleId, result]) => {
      if (result.status === 'fail') {
        recommendations.push({
          ruleId,
          priority: 'high',
          recommendation: `Implement monitoring and controls for ${ruleId}`,
          impact: 'Compliance violation risk'
        });
      }
    });

    return recommendations;
  }

  async getComponentData(component, startDate, filters, dataSourceConfig) {
    const { type, dataSource = 'logs', config = {} } = component;
    const query = { timestamp: { $gte: startDate } };

    // Apply component-specific filters - fix field name
    if (filters.logLevel) query.logLevel = filters.logLevel;
    if (filters.source) query.source = filters.source;
    if (filters.host) query.host = filters.host;

    // Apply component-specific query modifications
    if (config.additionalFilters) {
      Object.assign(query, config.additionalFilters);
    }

    switch (type) {
      case 'chart':
        return this.getChartData(component, query);
      case 'table':
        return this.getTableData(component, query);
      case 'metric':
        return this.getMetricData(component, query);
      case 'map':
        return this.getMapData(component, query);
      case 'heatmap':
        return this.getHeatmapData(component, query);
      case 'timeline':
        return this.getTimelineData(component, query);
      case 'gauge':
        return this.getGaugeData(component, query);
      default:
        return { message: `Component type ${type} not supported` };
    }
  }

  async getChartData(component, query) {
    const { chartType = 'line', config = {} } = component;
    const { timeGrouping = 'hour', metric = 'count' } = config;

    switch (chartType) {
      case 'line':
      case 'area':
        return timeGrouping === 'day' ? this.getLogsByDay(query) : this.getLogsByHour(query);
      case 'bar':
        return this.getLogsBySource(query);
      case 'pie':
      case 'doughnut':
        return this.getLogsByLevel(query);
      case 'scatter':
        return this.getScatterData(query);
      default:
        return this.getLogsByHour(query);
    }
  }

  async getTableData(component, query) {
    const { config = {} } = component;
    const { limit = 100, columns = ['timestamp', 'logLevel', 'source', 'host', 'message'] } = config;

    const logs = await Log.find(query)
      .sort({ timestamp: -1 })
      .limit(limit)
      .select(columns.join(' '))
      .lean();

    return {
      columns,
      data: logs,
      totalCount: await Log.countDocuments(query)
    };
  }

  async getMetricData(component, query) {
    const { config = {} } = component;
    const { metrics = ['total', 'critical', 'error', 'warning'] } = config;

    const results = {};

    if (metrics.includes('total')) {
      results.total = await Log.countDocuments(query);
    }

    if (metrics.includes('critical')) {
      results.critical = await Log.countDocuments({ ...query, logLevel: 'critical' });
    }

    if (metrics.includes('error')) {
      results.error = await Log.countDocuments({ ...query, logLevel: 'error' });
    }

    if (metrics.includes('warning')) {
      results.warning = await Log.countDocuments({ ...query, logLevel: 'warning' });
    }

    if (metrics.includes('info')) {
      results.info = await Log.countDocuments({ ...query, logLevel: 'info' });
    }

    if (metrics.includes('alerts')) {
      results.alerts = await Alert.countDocuments({ triggeredAt: { $gte: query.timestamp.$gte } });
    }

    return results;
  }

  async getMapData(component, query) {
    const { config = {} } = component;
    const { geoField = 'host' } = config;

    const hostData = await this.getTopHosts(query);

    // Enhanced geo data with better mock coordinates based on host patterns
    return hostData.map((host, index) => {
      const hostName = host._id;
      let lat, lng, country = 'Unknown';

      // Simple geo-mapping based on host patterns
      if (hostName.includes('us-') || hostName.includes('usa')) {
        lat = 39.8283 + (Math.random() - 0.5) * 10;
        lng = -98.5795 + (Math.random() - 0.5) * 20;
        country = 'United States';
      } else if (hostName.includes('eu-') || hostName.includes('europe')) {
        lat = 54.5260 + (Math.random() - 0.5) * 10;
        lng = 15.2551 + (Math.random() - 0.5) * 20;
        country = 'Europe';
      } else if (hostName.includes('asia') || hostName.includes('jp-')) {
        lat = 35.6762 + (Math.random() - 0.5) * 10;
        lng = 139.6503 + (Math.random() - 0.5) * 20;
        country = 'Asia';
      } else {
        lat = Math.random() * 180 - 90;
        lng = Math.random() * 360 - 180;
      }

      return {
        location: hostName,
        count: host.count,
        criticalCount: host.criticalCount || 0,
        errorCount: host.errorCount || 0,
        lat,
        lng,
        country
      };
    });
  }

  async getHeatmapData(component, query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            hour: { $hour: '$timestamp' },
            dayOfWeek: { $dayOfWeek: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.dayOfWeek': 1, '_id.hour': 1 } }
    ]);
  }

  async getTimelineData(component, query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
            logLevel: '$logLevel'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);
  }

  async getGaugeData(component, query) {
    const { config = {} } = component;
    const { metric = 'errorRate', maxValue = 100 } = config;

    const total = await Log.countDocuments(query);
    const errors = await Log.countDocuments({ ...query, logLevel: { $in: ['critical', 'error'] } });

    const errorRate = total > 0 ? (errors / total) * 100 : 0;

    return {
      value: Math.round(errorRate * 100) / 100,
      maxValue,
      label: 'Error Rate %',
      status: errorRate > 10 ? 'critical' : errorRate > 5 ? 'warning' : 'good'
    };
  }

  async getScatterData(query) {
    return Log.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$host',
          avgProcessingTime: { $avg: '$metadata.processingTime' },
          logCount: { $sum: 1 },
          errorCount: { $sum: { $cond: [{ $in: ['$logLevel', ['critical', 'error']] }, 1, 0] } }
        }
      },
      {
        $project: {
          host: '$_id',
          x: '$avgProcessingTime',
          y: '$logCount',
          size: '$errorCount'
        }
      },
      { $limit: 50 }
    ]);
  }

  addPDFContent(doc, reportData) {
    const { type, data, metadata } = reportData;

    // Add report type and metadata
    doc.fontSize(14).text(`Report Type: ${type.toUpperCase()}`, { underline: true });
    doc.moveDown();

    if (metadata) {
      doc.fontSize(10)
        .text(`Time Range: ${metadata.timeRange || 'N/A'}`)
        .text(`Generated: ${metadata.generatedAt?.toLocaleString() || 'N/A'}`)
        .text(`Records: ${metadata.recordCount || 0}`);
      doc.moveDown();
    }

    // Add content based on report type
    if (type === 'analytics' && data) {
      this.addAnalyticsPDFContent(doc, data);
    } else if (type === 'compliance' && data) {
      this.addCompliancePDFContent(doc, data);
    } else if (type === 'custom' && data) {
      this.addCustomPDFContent(doc, data);
    }
  }

  addAnalyticsPDFContent(doc, data) {
    doc.fontSize(12).text('Analytics Summary', { underline: true });
    doc.moveDown();

    if (data.summary) {
      doc.text(`Total Logs: ${data.summary.totalLogs || 0}`)
        .text(`Critical Events: ${data.summary.criticalEvents || 0}`)
        .text(`Error Events: ${data.summary.errorEvents || 0}`)
        .text(`Warning Events: ${data.summary.warningEvents || 0}`)
        .text(`Info Events: ${data.summary.infoEvents || 0}`)
        .text(`Unique Hosts: ${data.summary.uniqueHosts || 0}`)
        .text(`Unique Sources: ${data.summary.uniqueSources || 0}`)
        .text(`Total Alerts: ${data.summary.totalAlerts || 0}`)
        .text(`Active Alerts: ${data.summary.activeAlerts || 0}`);
      doc.moveDown();
    }

    if (data.topHosts && data.topHosts.length > 0) {
      doc.text('Top Hosts by Activity:', { underline: true });
      data.topHosts.slice(0, 10).forEach(host => {
        doc.text(`${host._id}: ${host.count} events (${host.criticalCount || 0} critical, ${host.errorCount || 0} errors)`);
      });
      doc.moveDown();
    }

    if (data.logsBySource && data.logsBySource.length > 0) {
      doc.text('Log Sources:', { underline: true });
      data.logsBySource.slice(0, 10).forEach(source => {
        doc.text(`${source._id}: ${source.count} events`);
      });
      doc.moveDown();
    }

    if (data.alertsBySeverity && data.alertsBySeverity.length > 0) {
      doc.text('Alerts by Severity:', { underline: true });
      data.alertsBySeverity.forEach(alert => {
        doc.text(`${alert._id}: ${alert.count} alerts`);
      });
      doc.moveDown();
    }
  }

  addCompliancePDFContent(doc, data) {
    doc.fontSize(12).text(`Compliance Report - ${data.framework?.toUpperCase()}`, { underline: true });
    doc.moveDown();

    doc.text(`Compliance Score: ${data.complianceScore}%`)
      .text(`Total Rules: ${data.totalRules}`)
      .text(`Passed: ${data.passedRules}`)
      .text(`Failed: ${data.failedRules}`);
    doc.moveDown();

    if (data.metrics) {
      doc.text('Compliance Metrics:', { underline: true });
      doc.text(`Authentication Events: ${data.metrics.authenticationEvents}`)
        .text(`Access Control Events: ${data.metrics.accessControlEvents}`)
        .text(`Data Protection Events: ${data.metrics.dataProtectionEvents}`)
        .text(`Audit Events: ${data.metrics.auditEvents}`)
        .text(`Configuration Changes: ${data.metrics.configurationChanges}`)
        .text(`Failed Logins: ${data.metrics.failedLogins}`)
        .text(`Privilege Escalations: ${data.metrics.privilegeEscalations}`);
      doc.moveDown();
    }

    if (data.results) {
      doc.text('Rule Evaluation Results:', { underline: true });
      Object.entries(data.results).forEach(([ruleId, result]) => {
        const status = result.status === 'pass' ? '✓' : '✗';
        doc.text(`${status} ${result.ruleName || ruleId}: ${result.details}`);
      });
      doc.moveDown();
    }

    if (data.recommendations && data.recommendations.length > 0) {
      doc.text('Recommendations:', { underline: true });
      data.recommendations.forEach(rec => {
        doc.text(`• ${rec.recommendation} (Priority: ${rec.priority})`);
      });
    }
  }

  addCustomPDFContent(doc, data) {
    doc.fontSize(12).text('Custom Report Data', { underline: true });
    doc.moveDown();

    Object.entries(data).forEach(([componentId, componentData]) => {
      doc.text(`Component: ${componentId}`, { underline: true });
      if (componentData.error) {
        doc.text(`Error: ${componentData.error}`);
      } else {
        doc.text(JSON.stringify(componentData, null, 2));
      }
      doc.moveDown();
    });
  }

  formatDataForCSV(reportData) {
    const { type, data } = reportData;

    if (type === 'analytics' && data) {
      return this.formatAnalyticsForCSV(data);
    } else if (type === 'compliance' && data) {
      return this.formatComplianceForCSV(data);
    } else if (type === 'custom' && data) {
      return this.formatCustomForCSV(data);
    }

    return [{ message: 'No data available for CSV export' }];
  }

  formatAnalyticsForCSV(data) {
    const csvData = [];

    // Add summary data
    if (data.summary) {
      csvData.push({
        category: 'Summary',
        metric: 'Total Logs',
        value: data.summary.totalLogs || 0,
        timestamp: new Date().toISOString()
      });
      csvData.push({
        category: 'Summary',
        metric: 'Critical Events',
        value: data.summary.criticalEvents || 0,
        timestamp: new Date().toISOString()
      });
      csvData.push({
        category: 'Summary',
        metric: 'Error Events',
        value: data.summary.errorEvents || 0,
        timestamp: new Date().toISOString()
      });
      csvData.push({
        category: 'Summary',
        metric: 'Warning Events',
        value: data.summary.warningEvents || 0,
        timestamp: new Date().toISOString()
      });
      csvData.push({
        category: 'Summary',
        metric: 'Info Events',
        value: data.summary.infoEvents || 0,
        timestamp: new Date().toISOString()
      });
      csvData.push({
        category: 'Summary',
        metric: 'Active Alerts',
        value: data.summary.activeAlerts || 0,
        timestamp: new Date().toISOString()
      });
    }

    // Add top hosts data with detailed metrics
    if (data.topHosts) {
      data.topHosts.forEach(host => {
        csvData.push({
          category: 'Top Hosts',
          metric: `${host._id} - Total Events`,
          value: host.count,
          additional_info: `Critical: ${host.criticalCount || 0}, Errors: ${host.errorCount || 0}`,
          timestamp: host.latestActivity || new Date().toISOString()
        });
      });
    }

    // Add log sources data
    if (data.logsBySource) {
      data.logsBySource.forEach(source => {
        csvData.push({
          category: 'Log Sources',
          metric: source._id,
          value: source.count,
          timestamp: source.latestTimestamp || new Date().toISOString()
        });
      });
    }

    // Add alerts by severity
    if (data.alertsBySeverity) {
      data.alertsBySeverity.forEach(alert => {
        csvData.push({
          category: 'Alerts by Severity',
          metric: alert._id,
          value: alert.count,
          timestamp: new Date().toISOString()
        });
      });
    }

    // Add hourly log data
    if (data.logsByHour) {
      data.logsByHour.forEach(hourData => {
        csvData.push({
          category: 'Hourly Logs',
          metric: hourData._id,
          value: hourData.count,
          additional_info: `Critical: ${hourData.criticalCount || 0}, Errors: ${hourData.errorCount || 0}, Warnings: ${hourData.warningCount || 0}`,
          timestamp: hourData._id
        });
      });
    }

    return csvData;
  }

  formatComplianceForCSV(data) {
    const csvData = [];

    // Add overall compliance data
    csvData.push({
      framework: data.framework,
      metric: 'Compliance Score',
      value: `${data.complianceScore}%`,
      timestamp: new Date().toISOString()
    });
    csvData.push({
      framework: data.framework,
      metric: 'Total Rules',
      value: data.totalRules,
      timestamp: new Date().toISOString()
    });
    csvData.push({
      framework: data.framework,
      metric: 'Passed Rules',
      value: data.passedRules,
      timestamp: new Date().toISOString()
    });
    csvData.push({
      framework: data.framework,
      metric: 'Failed Rules',
      value: data.failedRules,
      timestamp: new Date().toISOString()
    });

    // Add compliance metrics
    if (data.metrics) {
      Object.entries(data.metrics).forEach(([key, value]) => {
        csvData.push({
          framework: data.framework,
          metric: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
          value: value,
          category: 'Compliance Metrics',
          timestamp: new Date().toISOString()
        });
      });
    }

    // Add detailed rule results
    if (data.results) {
      Object.entries(data.results).forEach(([ruleId, result]) => {
        csvData.push({
          framework: data.framework,
          rule_id: ruleId,
          rule_name: result.ruleName || ruleId,
          category: result.ruleCategory || 'Unknown',
          status: result.status,
          score: result.score,
          evidence: result.evidence,
          details: result.details,
          last_checked: result.lastChecked,
          timestamp: new Date().toISOString()
        });
      });
    }

    // Add recommendations
    if (data.recommendations) {
      data.recommendations.forEach((rec, index) => {
        csvData.push({
          framework: data.framework,
          recommendation_id: index + 1,
          recommendation: rec.recommendation,
          priority: rec.priority,
          impact: rec.impact,
          rule_id: rec.ruleId,
          category: 'Recommendations',
          timestamp: new Date().toISOString()
        });
      });
    }

    return csvData;
  }

  formatCustomForCSV(data) {
    const csvData = [];

    Object.entries(data).forEach(([componentId, componentData]) => {
      if (componentData.error) {
        csvData.push({
          component: componentId,
          status: 'error',
          data: componentData.error
        });
      } else {
        csvData.push({
          component: componentId,
          status: 'success',
          data: JSON.stringify(componentData)
        });
      }
    });

    return csvData;
  }

  generateHTMLContent(reportData, title) {
    const { type, data, metadata } = reportData;
    const generatedDate = new Date().toLocaleString();
    const timeRange = metadata?.timeRange || '30d';

    let html = `
      <div class="header">
        <h1>${title}</h1>
        <p>Generated: ${generatedDate}</p>
        <p>Report Period: ${this.formatTimeRange(timeRange)}</p>
        <p>Report Type: ${type.charAt(0).toUpperCase() + type.slice(1)} Report</p>
      </div>
    `;

    if (type === 'analytics' && data) {
      html += this.generateComprehensiveAnalyticsReport(data, metadata);
    } else if (type === 'compliance' && data) {
      html += this.generateComprehensiveComplianceReport(data, metadata);
    } else if (type === 'custom' && data) {
      html += this.generateComprehensiveCustomReport(data, metadata);
    }

    return html;
  }

  formatTimeRange(timeRange) {
    const ranges = {
      '1h': 'Last 1 Hour',
      '24h': 'Last 24 Hours',
      '7d': 'Last 7 Days',
      '30d': 'Last 30 Days',
      '90d': 'Last 90 Days'
    };
    return ranges[timeRange] || 'Last 30 Days';
  }

  generateComprehensiveAnalyticsReport(data, metadata) {
    const summary = data.summary || {};
    const totalLogs = summary.totalLogs || 0;
    const criticalEvents = summary.criticalEvents || 0;
    const errorEvents = summary.errorEvents || 0;
    const warningEvents = summary.warningEvents || 0;
    const activeAlerts = summary.activeAlerts || 0;

    // Calculate key metrics
    const errorRate = totalLogs > 0 ? ((criticalEvents + errorEvents) / totalLogs * 100).toFixed(2) : 0;
    const securityScore = Math.max(0, 100 - (criticalEvents * 10 + errorEvents * 5 + warningEvents * 2));

    let html = `
      <!-- Executive Summary -->
      <div class="section">
        <h2>Executive Summary</h2>
        <p>This security analytics report provides a comprehensive overview of system activity, security events, and operational metrics for the specified time period. The analysis reveals key insights into system health, security posture, and areas requiring attention.</p>

        <h3>Key Findings</h3>
        <ul>
          <li><strong>System Activity:</strong> ${totalLogs.toLocaleString()} total log events processed</li>
          <li><strong>Security Posture:</strong> Overall security score of ${securityScore}/100</li>
          <li><strong>Error Rate:</strong> ${errorRate}% of events classified as errors or critical</li>
          <li><strong>Alert Status:</strong> ${activeAlerts} active security alerts requiring attention</li>
          ${criticalEvents > 0 ? `<li><strong>Critical Issues:</strong> ${criticalEvents} critical events detected requiring immediate action</li>` : ''}
        </ul>

        <h3>Risk Assessment</h3>
        <p>${this.generateRiskAssessment(criticalEvents, errorEvents, warningEvents, activeAlerts)}</p>
      </div>

      <!-- Metrics Overview -->
      <div class="section">
        <h2>Security Metrics Overview</h2>
        <div class="summary-cards">
          <div class="summary-card">
            <h3>Total Events</h3>
            <p class="metric-value">${totalLogs.toLocaleString()}</p>
          </div>
          <div class="summary-card critical">
            <h3>Critical Events</h3>
            <p class="metric-value">${criticalEvents}</p>
          </div>
          <div class="summary-card error">
            <h3>Error Events</h3>
            <p class="metric-value">${errorEvents}</p>
          </div>
          <div class="summary-card warning">
            <h3>Warning Events</h3>
            <p class="metric-value">${warningEvents}</p>
          </div>
          <div class="summary-card info">
            <h3>Security Score</h3>
            <p class="metric-value">${securityScore}/100</p>
          </div>
          <div class="summary-card">
            <h3>Active Alerts</h3>
            <p class="metric-value">${activeAlerts}</p>
          </div>
        </div>
      </div>`;

    // System Analysis Section
    if (data.topHosts && data.topHosts.length > 0) {
      html += `
        <div class="section">
          <h2>System Analysis</h2>
          <p>This section provides detailed analysis of system activity across different hosts and sources, identifying patterns and potential areas of concern.</p>

          <h3>Host Activity Analysis</h3>
          <p>The following table shows the most active hosts in your environment. High error rates may indicate system issues requiring investigation.</p>
          <table class="data-table">
            <thead>
              <tr>
                <th>Host</th>
                <th>Total Events</th>
                <th>Critical</th>
                <th>Errors</th>
                <th>Error Rate</th>
                <th>Last Activity</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              ${data.topHosts.slice(0, 10).map(host => {
                const errorRate = host.count > 0 ? (((host.criticalCount || 0) + (host.errorCount || 0)) / host.count * 100).toFixed(1) : 0;
                const status = errorRate > 10 ? 'High Risk' : errorRate > 5 ? 'Medium Risk' : 'Normal';
                const statusClass = errorRate > 10 ? 'critical' : errorRate > 5 ? 'warning' : 'info';
                return `<tr>
                  <td>${host._id}</td>
                  <td>${host.count.toLocaleString()}</td>
                  <td class="critical">${host.criticalCount || 0}</td>
                  <td class="error">${host.errorCount || 0}</td>
                  <td>${errorRate}%</td>
                  <td>${host.latestActivity ? new Date(host.latestActivity).toLocaleString() : 'N/A'}</td>
                  <td class="${statusClass}">${status}</td>
                </tr>`;
              }).join('')}
            </tbody>
          </table>
        </div>`;
    }

    if (data.logsBySource && data.logsBySource.length > 0) {
      html += `
        <div class="section">
          <h2>Log Source Analysis</h2>
          <p>Understanding log source distribution helps identify which systems are most active and may require additional monitoring or resources.</p>

          <table class="data-table">
            <thead>
              <tr>
                <th>Source</th>
                <th>Event Count</th>
                <th>Percentage</th>
                <th>Latest Activity</th>
                <th>Health Status</th>
              </tr>
            </thead>
            <tbody>
              ${data.logsBySource.slice(0, 10).map(source => {
                const percentage = totalLogs > 0 ? (source.count / totalLogs * 100).toFixed(1) : 0;
                const isStale = source.latestTimestamp && (Date.now() - new Date(source.latestTimestamp).getTime()) > 24 * 60 * 60 * 1000;
                const healthStatus = isStale ? 'Stale' : 'Active';
                const healthClass = isStale ? 'warning' : 'info';
                return `<tr>
                  <td>${source._id}</td>
                  <td>${source.count.toLocaleString()}</td>
                  <td>${percentage}%</td>
                  <td>${source.latestTimestamp ? new Date(source.latestTimestamp).toLocaleString() : 'N/A'}</td>
                  <td class="${healthClass}">${healthStatus}</td>
                </tr>`;
              }).join('')}
            </tbody>
          </table>
        </div>`;
    }

    if (data.alertsBySeverity && data.alertsBySeverity.length > 0) {
      html += `
        <div class="section">
          <h2>Security Alert Analysis</h2>
          <p>Security alerts indicate potential threats or policy violations that require investigation and response.</p>

          <table class="data-table">
            <thead>
              <tr>
                <th>Severity</th>
                <th>Alert Count</th>
                <th>Percentage</th>
                <th>Response Priority</th>
              </tr>
            </thead>
            <tbody>
              ${data.alertsBySeverity.map(alert => {
                const totalAlerts = data.alertsBySeverity.reduce((sum, a) => sum + a.count, 0);
                const percentage = totalAlerts > 0 ? (alert.count / totalAlerts * 100).toFixed(1) : 0;
                const priority = alert._id === 'critical' ? 'Immediate' : alert._id === 'high' ? 'Within 1 Hour' : alert._id === 'medium' ? 'Within 24 Hours' : 'Within 1 Week';
                return `<tr>
                  <td class="${alert._id}">${alert._id.toUpperCase()}</td>
                  <td>${alert.count}</td>
                  <td>${percentage}%</td>
                  <td>${priority}</td>
                </tr>`;
              }).join('')}
            </tbody>
          </table>
        </div>`;
    }

    // Recommendations Section
    html += `
      <div class="section">
        <h2>Recommendations & Action Items</h2>
        <p>Based on the analysis of your security data, the following recommendations are provided to improve your security posture:</p>

        <h3>Immediate Actions Required</h3>
        <ul>
          ${this.generateRecommendations(data, 'immediate').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>

        <h3>Short-term Improvements (1-4 weeks)</h3>
        <ul>
          ${this.generateRecommendations(data, 'short-term').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>

        <h3>Long-term Strategic Initiatives (1-6 months)</h3>
        <ul>
          ${this.generateRecommendations(data, 'long-term').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>
      </div>

      <div class="section">
        <h2>Conclusion</h2>
        <p>This security analytics report provides insights into your current security posture. Regular monitoring and proactive response to the identified issues will help maintain a strong security stance. Consider implementing the recommended actions based on their priority levels.</p>
        <p><strong>Next Report:</strong> Schedule the next security analytics report for ${this.getNextReportDate()} to maintain continuous monitoring.</p>
      </div>`;

    return html;
  }

  generateComprehensiveComplianceReport(data, metadata) {
    const framework = data.framework?.toUpperCase() || 'COMPLIANCE';
    const complianceScore = data.complianceScore || 0;
    const passedRules = data.passedRules || 0;
    const totalRules = data.totalRules || 0;
    const failedRules = totalRules - passedRules;

    return `
      <!-- Executive Summary -->
      <div class="section">
        <h2>Executive Summary</h2>
        <p>This ${framework} compliance report provides a comprehensive assessment of your organization's adherence to ${framework} requirements. The evaluation covers ${totalRules} compliance controls and provides actionable recommendations for maintaining and improving compliance posture.</p>

        <h3>Compliance Status Overview</h3>
        <ul>
          <li><strong>Overall Compliance Score:</strong> ${complianceScore}% (${this.getComplianceRating(complianceScore)})</li>
          <li><strong>Controls Passed:</strong> ${passedRules} out of ${totalRules} controls</li>
          <li><strong>Controls Failed:</strong> ${failedRules} controls requiring attention</li>
          <li><strong>Risk Level:</strong> ${this.getComplianceRiskLevel(complianceScore)}</li>
        </ul>
      </div>

      <!-- Compliance Metrics -->
      <div class="section">
        <h2>${framework} Compliance Metrics</h2>
        <div class="summary-cards">
          <div class="summary-card ${complianceScore >= 90 ? 'info' : complianceScore >= 70 ? 'warning' : 'critical'}">
            <h3>Compliance Score</h3>
            <p class="metric-value">${complianceScore}%</p>
          </div>
          <div class="summary-card info">
            <h3>Controls Passed</h3>
            <p class="metric-value">${passedRules}</p>
          </div>
          <div class="summary-card ${failedRules > 0 ? 'error' : 'info'}">
            <h3>Controls Failed</h3>
            <p class="metric-value">${failedRules}</p>
          </div>
          <div class="summary-card">
            <h3>Total Controls</h3>
            <p class="metric-value">${totalRules}</p>
          </div>
        </div>
      </div>

      <!-- Detailed Assessment -->
      <div class="section">
        <h2>Detailed Control Assessment</h2>
        <p>The following table provides a detailed breakdown of each compliance control, its current status, and evidence supporting the assessment.</p>

        ${data.results ? this.generateComplianceDetailsTable(data.results) : '<p>No detailed results available.</p>'}
      </div>

      <!-- Recommendations -->
      <div class="section">
        <h2>Compliance Recommendations</h2>
        <p>Based on the compliance assessment, the following recommendations are provided to achieve and maintain full compliance:</p>

        <h3>Critical Actions (Immediate)</h3>
        <ul>
          ${this.generateComplianceRecommendations(data, 'critical').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>

        <h3>Improvement Opportunities (30-90 days)</h3>
        <ul>
          ${this.generateComplianceRecommendations(data, 'improvement').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>

        <h3>Best Practices (Ongoing)</h3>
        <ul>
          ${this.generateComplianceRecommendations(data, 'best-practice').map(rec => `<li><strong>${rec.title}:</strong> ${rec.description}</li>`).join('')}
        </ul>
      </div>

      <!-- Conclusion -->
      <div class="section">
        <h2>Conclusion & Next Steps</h2>
        <p>Your organization has achieved a ${complianceScore}% compliance score for ${framework}. ${this.getComplianceConclusion(complianceScore, failedRules)}</p>

        <h3>Recommended Timeline</h3>
        <ul>
          <li><strong>Next Assessment:</strong> ${this.getNextAssessmentDate()}</li>
          <li><strong>Remediation Review:</strong> ${this.getRemediationReviewDate()}</li>
          <li><strong>Annual Audit Preparation:</strong> Ensure all controls are documented and evidence is current</li>
        </ul>
      </div>
    `;
  }

  generateRiskAssessment(criticalEvents, errorEvents, warningEvents, activeAlerts) {
    const totalIssues = criticalEvents + errorEvents + warningEvents + activeAlerts;

    if (totalIssues === 0) {
      return "Current risk level is LOW. System appears to be operating normally with no critical issues detected.";
    } else if (criticalEvents > 10 || activeAlerts > 20) {
      return "Current risk level is HIGH. Multiple critical events and active alerts require immediate attention to prevent potential security incidents.";
    } else if (criticalEvents > 0 || errorEvents > 50 || activeAlerts > 10) {
      return "Current risk level is MEDIUM. Some issues detected that should be addressed promptly to maintain security posture.";
    } else {
      return "Current risk level is LOW-MEDIUM. Minor issues detected that should be monitored and addressed during regular maintenance windows.";
    }
  }

  generateRecommendations(data, timeframe) {
    const recommendations = [];
    const summary = data.summary || {};

    if (timeframe === 'immediate') {
      if (summary.criticalEvents > 0) {
        recommendations.push({
          title: "Address Critical Events",
          description: `Investigate and resolve ${summary.criticalEvents} critical events immediately to prevent potential security breaches.`
        });
      }
      if (summary.activeAlerts > 20) {
        recommendations.push({
          title: "Alert Triage",
          description: `Review and triage ${summary.activeAlerts} active alerts to identify false positives and genuine threats.`
        });
      }
      if (data.topHosts && data.topHosts.some(host => ((host.criticalCount || 0) + (host.errorCount || 0)) / host.count > 0.1)) {
        recommendations.push({
          title: "High Error Rate Hosts",
          description: "Investigate hosts with error rates above 10% as they may indicate system instability or security issues."
        });
      }
    } else if (timeframe === 'short-term') {
      recommendations.push({
        title: "Enhance Monitoring Coverage",
        description: "Review log sources to ensure comprehensive coverage of all critical systems and applications."
      });
      if (summary.warningEvents > 100) {
        recommendations.push({
          title: "Warning Event Analysis",
          description: "Analyze patterns in warning events to identify potential issues before they become critical."
        });
      }
      recommendations.push({
        title: "Alert Rule Optimization",
        description: "Review and tune alert rules to reduce false positives while maintaining security coverage."
      });
    } else if (timeframe === 'long-term') {
      recommendations.push({
        title: "Security Metrics Dashboard",
        description: "Implement real-time security metrics dashboard for continuous monitoring and faster incident response."
      });
      recommendations.push({
        title: "Automated Response Procedures",
        description: "Develop automated response procedures for common security events to reduce response time."
      });
      recommendations.push({
        title: "Security Training Program",
        description: "Implement regular security training for operations team to improve incident response capabilities."
      });
    }

    return recommendations.length > 0 ? recommendations : [{ title: "No specific recommendations", description: "System appears to be operating within normal parameters." }];
  }

  getNextReportDate() {
    const nextDate = new Date();
    nextDate.setDate(nextDate.getDate() + 7); // Weekly reports
    return nextDate.toLocaleDateString();
  }

  getComplianceRating(score) {
    if (score >= 95) return 'Excellent';
    if (score >= 85) return 'Good';
    if (score >= 70) return 'Satisfactory';
    if (score >= 50) return 'Needs Improvement';
    return 'Poor';
  }

  getComplianceRiskLevel(score) {
    if (score >= 90) return 'Low Risk';
    if (score >= 70) return 'Medium Risk';
    if (score >= 50) return 'High Risk';
    return 'Critical Risk';
  }

  generateComplianceDetailsTable(results) {
    if (!results || Object.keys(results).length === 0) {
      return '<p>No compliance results available.</p>';
    }

    return `
      <table class="data-table">
        <thead>
          <tr>
            <th>Control ID</th>
            <th>Control Name</th>
            <th>Category</th>
            <th>Status</th>
            <th>Score</th>
            <th>Evidence</th>
            <th>Last Checked</th>
          </tr>
        </thead>
        <tbody>
          ${Object.entries(results).map(([ruleId, result]) => `
            <tr>
              <td>${ruleId}</td>
              <td>${result.ruleName || 'N/A'}</td>
              <td>${result.ruleCategory || 'N/A'}</td>
              <td class="${result.status}">${result.status?.toUpperCase() || 'UNKNOWN'}</td>
              <td>${result.score || 0}%</td>
              <td>${result.evidence || 'No evidence'}</td>
              <td>${result.lastChecked ? new Date(result.lastChecked).toLocaleDateString() : 'N/A'}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;
  }

  generateComplianceRecommendations(data, type) {
    const recommendations = [];
    const failedRules = data.totalRules - data.passedRules;

    if (type === 'critical') {
      if (failedRules > 0) {
        recommendations.push({
          title: "Address Failed Controls",
          description: `Immediately remediate ${failedRules} failed compliance controls to reduce regulatory risk.`
        });
      }
      if (data.complianceScore < 70) {
        recommendations.push({
          title: "Compliance Program Review",
          description: "Conduct comprehensive review of compliance program and implement corrective measures."
        });
      }
    } else if (type === 'improvement') {
      recommendations.push({
        title: "Documentation Enhancement",
        description: "Improve documentation and evidence collection for all compliance controls."
      });
      recommendations.push({
        title: "Automated Monitoring",
        description: "Implement automated monitoring for compliance controls to ensure continuous adherence."
      });
      if (data.complianceScore < 90) {
        recommendations.push({
          title: "Control Optimization",
          description: "Optimize existing controls and implement additional safeguards to achieve higher compliance scores."
        });
      }
    } else if (type === 'best-practice') {
      recommendations.push({
        title: "Regular Assessments",
        description: "Conduct quarterly compliance assessments to maintain continuous compliance posture."
      });
      recommendations.push({
        title: "Staff Training",
        description: "Provide regular compliance training to all relevant staff members."
      });
      recommendations.push({
        title: "Vendor Management",
        description: "Ensure all third-party vendors meet compliance requirements through regular assessments."
      });
    }

    return recommendations.length > 0 ? recommendations : [{ title: "No specific recommendations", description: "Compliance posture appears adequate." }];
  }

  getComplianceConclusion(score, failedRules) {
    if (score >= 95) {
      return "This represents excellent compliance posture with minimal risk exposure. Continue current practices and maintain regular monitoring.";
    } else if (score >= 85) {
      return "This represents good compliance posture with manageable risk. Focus on addressing remaining gaps to achieve excellence.";
    } else if (score >= 70) {
      return "This represents satisfactory compliance but with notable gaps that require attention to reduce regulatory risk.";
    } else {
      return "This represents significant compliance gaps that require immediate attention to avoid regulatory penalties and reduce organizational risk.";
    }
  }

  getNextAssessmentDate() {
    const nextDate = new Date();
    nextDate.setMonth(nextDate.getMonth() + 3); // Quarterly assessments
    return nextDate.toLocaleDateString();
  }

  getRemediationReviewDate() {
    const nextDate = new Date();
    nextDate.setDate(nextDate.getDate() + 30); // 30-day remediation review
    return nextDate.toLocaleDateString();
  }

  generateComprehensiveCustomReport(data, metadata) {
    let html = `
      <div class="section">
        <h2>Custom Report Analysis</h2>
        <p>This custom report provides detailed analysis of specific metrics and data points as configured for your organization's unique requirements.</p>
      </div>
    `;

    Object.entries(data).forEach(([componentId, componentData]) => {
      html += `
        <div class="section">
          <h2>Component: ${componentId}</h2>
      `;
      if (componentData.error) {
        html += `<p style="color: red;">Error: ${componentData.error}</p>`;
      } else {
        html += `<pre>${JSON.stringify(componentData, null, 2)}</pre>`;
      }
      html += '</div>';
    });

    return html;
  }
}

module.exports = new ReportService();
