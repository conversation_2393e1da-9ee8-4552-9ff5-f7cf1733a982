{"_message":"User validation failed","errors":{"apiKeys.0.permissions.0":{"kind":"enum","message":"`ingest_logs` is not a valid enum value for path `permissions.0`.","name":"ValidatorError","path":"permissions.0","properties":{"enumValues":["view_logs","search_logs","export_logs","view_alerts","manage_alerts","view_agents","manage_agents","view_reports","generate_reports"],"message":"`ingest_logs` is not a valid enum value for path `permissions.0`.","path":"permissions.0","type":"enum","value":"ingest_logs"},"value":"ingest_logs"}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAPI key authentication error: User validation failed: apiKeys.0.permissions.0: `ingest_logs` is not a valid enum value for path `permissions.0`.\u001b[39m","stack":"ValidationError: User validation failed: apiKeys.0.permissions.0: `ingest_logs` is not a valid enum value for path `permissions.0`.\n    at Document.invalidate (/app/node_modules/mongoose/lib/document.js:3343:32)\n    at Subdocument.invalidate (/app/node_modules/mongoose/lib/types/subdocument.js:229:12)\n    at /app/node_modules/mongoose/lib/document.js:3104:17\n    at /app/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-20 18:29:55:2955"}
{"ip":"************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:202:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:31:57:3157","url":"/api/v1/logs","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-CA) WindowsPowerShell/5.1.19041.5965","userId":"6855a02042f295c34575d31d"}
