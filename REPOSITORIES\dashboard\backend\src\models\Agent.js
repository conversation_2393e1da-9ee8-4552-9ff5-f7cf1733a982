const mongoose = require('mongoose');

const agentSchema = new mongoose.Schema({
  agentId: {
    type: String,
    required: true,
    unique: true,
    index: true,
    trim: true,
    maxlength: 400, // Increased to accommodate userId_hostname format
  },
  hostname: {
    type: String,
    required: true,
    index: true,
    trim: true,
    maxlength: 255,
  },
  ipAddress: {
    type: String,
    required: false,
    trim: true,
    maxlength: 45, // IPv6 max length
  },
  platform: {
    type: String,
    required: true,
    enum: ['windows', 'linux', 'macos', 'other'],
    index: true,
  },
  version: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  status: {
    type: String,
    required: true,
    enum: ['online', 'offline', 'warning', 'error'],
    default: 'offline',
    index: true,
  },
  lastHeartbeat: {
    type: Date,
    required: true,
    default: Date.now,
    index: true,
  },
  firstSeen: {
    type: Date,
    required: true,
    default: Date.now,
    index: true,
  },
  lastSeen: {
    type: Date,
    required: true,
    default: Date.now,
    index: true,
  },
  configuration: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
  metadata: {
    operatingSystem: String,
    osVersion: String,
    architecture: String,
    totalMemory: Number,
    availableMemory: Number,
    cpuUsage: Number,
    diskUsage: Number,
    agentVersion: String,
    configVersion: String,
    logSources: [{
      name: String,
      enabled: Boolean,
      lastCollection: Date,
    }],
    performance: {
      logsProcessed: {
        type: Number,
        default: 0,
      },
      logsPerSecond: {
        type: Number,
        default: 0,
      },
      errorCount: {
        type: Number,
        default: 0,
      },
      lastError: {
        timestamp: Date,
        message: String,
        code: String,
      },
    },
    network: {
      connectionType: String,
      latency: Number,
      bandwidth: Number,
    },
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 50,
  }],
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true,
      maxlength: 1000,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  alertsEnabled: {
    type: Boolean,
    default: true,
  },
  maintenanceMode: {
    type: Boolean,
    default: false,
    index: true,
  },
  maintenanceWindow: {
    start: Date,
    end: Date,
    reason: String,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for efficient querying
agentSchema.index({ status: 1, lastHeartbeat: -1 });
agentSchema.index({ hostname: 1, platform: 1 });
agentSchema.index({ isActive: 1, status: 1 });
agentSchema.index({ tags: 1, status: 1 });
agentSchema.index({ createdAt: -1 });

// Virtual for agent uptime
agentSchema.virtual('uptimeMinutes').get(function() {
  if (this.status === 'offline') return 0;
  return Math.floor((Date.now() - this.lastHeartbeat.getTime()) / (1000 * 60));
});

// Virtual for connection status
agentSchema.virtual('connectionStatus').get(function() {
  const now = Date.now();
  const lastHeartbeat = this.lastHeartbeat.getTime();
  const minutesSinceHeartbeat = (now - lastHeartbeat) / (1000 * 60);
  
  if (minutesSinceHeartbeat <= 2) return 'online';
  if (minutesSinceHeartbeat <= 10) return 'warning';
  return 'offline';
});

// Virtual for formatted last seen
agentSchema.virtual('formattedLastSeen').get(function() {
  return this.lastSeen.toISOString();
});

// Method to update heartbeat
agentSchema.methods.updateHeartbeat = function(metadata = {}) {
  this.lastHeartbeat = new Date();
  this.lastSeen = new Date();
  
  // Update metadata if provided
  if (Object.keys(metadata).length > 0) {
    this.metadata = { ...this.metadata, ...metadata };
  }
  
  // Update status based on heartbeat
  this.status = this.connectionStatus;
  
  return this.save();
};

// Method to check if agent is online
agentSchema.methods.isOnline = function() {
  const minutesSinceHeartbeat = (Date.now() - this.lastHeartbeat.getTime()) / (1000 * 60);
  return minutesSinceHeartbeat <= 2;
};

// Method to check if agent needs attention
agentSchema.methods.needsAttention = function() {
  return this.status === 'error' || 
         this.status === 'warning' || 
         (this.metadata?.performance?.errorCount > 10);
};

// Static method to find active agents
agentSchema.statics.findActive = function() {
  return this.find({ isActive: true }).sort({ lastHeartbeat: -1 });
};

// Static method to find online agents
agentSchema.statics.findOnline = function() {
  const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
  return this.find({ 
    isActive: true, 
    lastHeartbeat: { $gte: twoMinutesAgo } 
  }).sort({ lastHeartbeat: -1 });
};

// Static method to find offline agents
agentSchema.statics.findOffline = function() {
  const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
  return this.find({ 
    isActive: true, 
    lastHeartbeat: { $lt: tenMinutesAgo } 
  }).sort({ lastHeartbeat: -1 });
};

// Pre-save middleware to update status
agentSchema.pre('save', function(next) {
  if (this.isModified('lastHeartbeat')) {
    this.status = this.connectionStatus;
  }
  next();
});

module.exports = mongoose.model('Agent', agentSchema);
