const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api/v1';

async function testAgentsAPI() {
  try {
    console.log('🧪 Testing Agents API...\n');

    // First, login to get a token
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });

    const token = loginResponse.data.data.token;
    console.log('   ✅ Login successful\n');

    // Set up headers for authenticated requests
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test 1: Get agents list
    console.log('2️⃣ Testing GET /agents...');
    try {
      const agentsResponse = await axios.get(`${API_BASE_URL}/agents`, { headers });
      console.log('   ✅ Agents list retrieved successfully');
      console.log(`   📊 Found ${agentsResponse.data.data.agents.length} agents`);
      console.log(`   📈 Summary: ${JSON.stringify(agentsResponse.data.data.summary)}`);
    } catch (error) {
      console.log('   ❌ Failed to get agents list:', error.response?.data?.message || error.message);
    }

    // Test 2: Get agent stats
    console.log('\n3️⃣ Testing GET /agents/stats...');
    try {
      const statsResponse = await axios.get(`${API_BASE_URL}/agents/stats`, { headers });
      console.log('   ✅ Agent stats retrieved successfully');
      console.log(`   📊 Stats: ${JSON.stringify(statsResponse.data.data.summary)}`);
    } catch (error) {
      console.log('   ❌ Failed to get agent stats:', error.response?.data?.message || error.message);
    }

    // Test 3: Test agent download endpoint
    console.log('\n4️⃣ Testing GET /agents/download/windows...');
    try {
      const downloadResponse = await axios.get(`${API_BASE_URL}/agents/download/windows`, { 
        headers,
        responseType: 'stream'
      });
      console.log('   ✅ Agent download endpoint working');
      console.log(`   📦 Content-Type: ${downloadResponse.headers['content-type']}`);
      console.log(`   📏 Content-Length: ${downloadResponse.headers['content-length']} bytes`);
    } catch (error) {
      console.log('   ❌ Failed to test download:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 Agents API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testAgentsAPI();
