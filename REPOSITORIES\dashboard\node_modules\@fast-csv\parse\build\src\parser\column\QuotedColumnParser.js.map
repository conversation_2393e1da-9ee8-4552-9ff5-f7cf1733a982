{"version": 3, "file": "QuotedColumnParser.js", "sourceRoot": "", "sources": ["../../../../src/parser/column/QuotedColumnParser.ts"], "names": [], "mappings": ";;;AAAA,uDAAoD;AAGpD,oCAAiC;AAOjC,MAAa,kBAAkB;IACV,aAAa,CAAgB;IAE7B,eAAe,CAAkB;IAElD,YAAmB,aAA4B;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK,CAAC,OAAgB;QACzB,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;QACtC,MAAM,EAAE,iBAAiB,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,mCAAmC;YACnC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAClC,2FAA2F;YAC3F,mCAAmC;YACnC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CACX,kCACI,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,EAChC,kBAAkB,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CACzE,CAAC;YACN,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,GAAG,CAAC;IACf,CAAC;IAEO,uBAAuB,CAAC,OAAgB;QAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,SAAS,GAAiB,OAAO,CAAC,kBAAkB,CAAC;QACzD,OAAO,CAAC,iBAAiB,IAAI,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACtF,MAAM,OAAO,GAAG,aAAK,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC7D,qBAAqB;YACrB,IAAI,CAAC,kBAAkB,IAAI,OAAO,EAAE,CAAC;gBACjC,kBAAkB,GAAG,IAAI,CAAC;YAC9B,CAAC;iBAAM,IAAI,kBAAkB,EAAE,CAAC;gBAC5B,IAAI,aAAK,CAAC,sBAAsB,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC;oBACzD,uEAAuE;oBACvE,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;oBACpC,MAAM,oBAAoB,GAAG,OAAO,CAAC,kBAAkB,CAAC;oBACxD,2EAA2E;oBAC3E,0CAA0C;oBAC1C,IACI,oBAAoB,KAAK,IAAI;wBAC7B,CAAC,aAAK,CAAC,YAAY,CAAC,oBAAoB,EAAE,aAAa,CAAC;4BACpD,aAAK,CAAC,sBAAsB,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC,EACxE,CAAC;wBACC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBAC5C,SAAS,GAAG,oBAAoB,CAAC;oBACrC,CAAC;yBAAM,IAAI,OAAO,EAAE,CAAC;wBACjB,iFAAiF;wBACjF,iBAAiB,GAAG,IAAI,CAAC;oBAC7B,CAAC;yBAAM,CAAC;wBACJ,oFAAoF;wBACpF,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBACrC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,EAAE,CAAC;oBACjB,8BAA8B;oBAC9B,iBAAiB,GAAG,IAAI,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACJ,kCAAkC;oBAClC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;YACD,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC;IACxF,CAAC;IAEO,uBAAuB,CAAC,OAAgB;QAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,MAAM,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QACtC,IAAI,iBAAiB,EAAE,CAAC;YACpB,MAAM,qBAAqB,GAAG,aAAK,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;YACvF,MAAM,wBAAwB,GAAG,aAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YAC9E,IAAI,CAAC,CAAC,qBAAqB,IAAI,wBAAwB,CAAC,EAAE,CAAC;gBACvD,iGAAiG;gBACjG,2CAA2C;gBAC3C,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACpF,MAAM,IAAI,KAAK,CACX,2BAA2B,aAAa,CAAC,gBAAgB,uBAAuB,iBAAiB,CAAC,KAAK,UAAU,WAAW,GAAG,CAClI,CAAC;YACN,CAAC;YACD,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9B,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;CACJ;AAlGD,gDAkGC"}