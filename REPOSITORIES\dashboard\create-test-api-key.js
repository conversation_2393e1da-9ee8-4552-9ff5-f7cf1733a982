// Script to create a proper API key for testing
db = db.getSiblingDB('exlog');

// Remove existing API keys
db.users.updateOne(
  { username: 'system' },
  { $set: { apiKeys: [] } }
);

// Add new API key with valid permissions
const result = db.users.updateOne(
  { username: 'system' },
  {
    $push: {
      apiKeys: {
        key: 'test-api-key-12345',
        name: 'Test Key for Log Ingestion',
        description: 'API key for testing log ingestion and retrieval',
        permissions: ['view_logs', 'search_logs', 'export_logs'],
        isActive: true,
        createdAt: new Date(),
        lastUsed: null,
        usageCount: 0
      }
    }
  }
);

print('API key created:', JSON.stringify(result));

// Verify the key was added
const user = db.users.findOne({ username: 'system' }, { apiKeys: 1 });
print('System user API keys:', JSON.stringify(user.apiKeys, null, 2));
