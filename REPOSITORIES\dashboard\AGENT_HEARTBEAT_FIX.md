# Agent Heartbeat Fix - Unique Agent Identification

## Problem Description

The agent heartbeat system was incorrectly identifying agents from different systems as the same agent when they used the same API key (user ID). This happened because:

1. **Root Cause**: The `agentId` was set to just the user's database ID (`req.user._id.toString()`)
2. **Impact**: Agents on different systems with the same API key were treated as one agent
3. **Symptoms**: 
   - Agent hostname would change when different systems sent heartbeats
   - Only one agent would appear in the dashboard instead of multiple
   - Agent status would be inconsistent

## Solution

The fix creates unique agent IDs by combining the user ID with the hostname:

```javascript
// Old format: "507f1f77bcf86cd799439011"
// New format: "507f1f77bcf86cd799439011_DESKTOP-ABC123"
const uniqueAgentId = `${req.user._id.toString()}_${hostname}`;
```

## Files Modified

### Backend Changes

1. **`backend/src/routes/logs.js`**
   - Updated agent tracking in log ingestion to use unique agent IDs
   - Modified agent lookup and creation logic

2. **`backend/src/routes/agents.js`**
   - Updated agent registration endpoint to use unique agent IDs
   - Modified heartbeat endpoint to support hostname parameter

3. **`backend/src/models/Agent.js`**
   - Increased `agentId` maxlength from 100 to 400 characters

### Migration Scripts

4. **`backend/migrations/migrate-agent-ids.js`**
   - Script to migrate existing agents to new ID format

5. **`backend/test-agent-fix.js`**
   - Test script to verify the fix works correctly

## Deployment Steps

### 1. Apply Code Changes

The code changes have been applied to:
- Log ingestion endpoint
- Agent registration endpoint  
- Agent heartbeat endpoint
- Agent model schema

### 2. Run Migration (Important!)

Before deploying to production, run the migration script to update existing agents:

```bash
cd backend
node migrations/migrate-agent-ids.js
```

This will:
- Find agents with old format IDs (without underscore)
- Update them to new format: `userId_hostname`
- Skip duplicates if they already exist
- Report migration results

### 3. Test the Fix

Run the test script to verify everything works:

```bash
cd backend
node test-agent-fix.js
```

This will:
- Create two test agents with same user ID but different hostnames
- Verify they are treated as separate agents
- Test that updates to one don't affect the other
- Clean up test data

### 4. Deploy Application

After successful migration and testing:

```bash
docker compose down
docker compose build
docker compose up -d
```

## Verification

After deployment, verify the fix by:

1. **Check Agent List**: Agents from different systems should appear separately
2. **Monitor Heartbeats**: Each system should maintain its own heartbeat status
3. **Test Agent Updates**: Changes to one agent shouldn't affect others

## API Changes

### Agent Registration

The registration endpoint now creates unique agent IDs automatically:

```javascript
// Before: agentId = "507f1f77bcf86cd799439011"
// After:  agentId = "507f1f77bcf86cd799439011_DESKTOP-ABC123"
```

### Agent Heartbeat

The heartbeat endpoint now accepts an optional `hostname` parameter:

```javascript
POST /api/v1/agents/:agentId/heartbeat
{
  "metadata": { ... },
  "hostname": "DESKTOP-ABC123"  // Optional, for backward compatibility
}
```

## Backward Compatibility

- Existing agents will be migrated automatically
- Old API calls without hostname will still work
- New agent installations will use the new format automatically

## Troubleshooting

### Migration Issues

If migration fails:
1. Check database connectivity
2. Verify no duplicate hostnames exist for the same user
3. Run migration script with verbose logging

### Agent Not Appearing

If agents still don't appear separately:
1. Verify agent is sending correct hostname in logs
2. Check agent registration uses new format
3. Confirm migration completed successfully

### Performance Impact

The fix has minimal performance impact:
- Agent ID lookups remain indexed
- No additional database queries required
- Slightly longer agent IDs (negligible storage impact)

## Future Considerations

- Consider adding system UUID as additional uniqueness factor
- Monitor for hostname collisions in large deployments
- Add agent grouping features for better organization
