/**
 * Test script to verify the agent ID fix works correctly
 * This script simulates agents from different systems with the same API key
 */

const mongoose = require('mongoose');
const Agent = require('./src/models/Agent');

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
};

const testAgentFix = async () => {
  try {
    console.log('Testing agent ID fix...');
    
    // Simulate the same user ID (API key) from different systems
    const userId = '507f1f77bcf86cd799439011'; // Example user ID
    const system1Hostname = 'DESKTOP-ABC123';
    const system2Hostname = 'LAPTOP-XYZ789';
    
    // Create unique agent IDs as the fix would do
    const agent1Id = `${userId}_${system1Hostname}`;
    const agent2Id = `${userId}_${system2Hostname}`;
    
    console.log(`Agent 1 ID: ${agent1Id}`);
    console.log(`Agent 2 ID: ${agent2Id}`);
    
    // Clean up any existing test agents
    await Agent.deleteMany({ 
      agentId: { $in: [agent1Id, agent2Id] }
    });
    
    // Create first agent (System 1)
    const agent1 = new Agent({
      agentId: agent1Id,
      hostname: system1Hostname,
      platform: 'windows',
      version: '2.0.0',
      status: 'online',
      metadata: {
        operatingSystem: 'Windows',
        agentVersion: '2.0.0'
      }
    });
    
    await agent1.save();
    console.log(`✓ Created agent 1: ${agent1.agentId} (${agent1.hostname})`);
    
    // Create second agent (System 2)
    const agent2 = new Agent({
      agentId: agent2Id,
      hostname: system2Hostname,
      platform: 'windows',
      version: '2.0.0',
      status: 'online',
      metadata: {
        operatingSystem: 'Windows',
        agentVersion: '2.0.0'
      }
    });
    
    await agent2.save();
    console.log(`✓ Created agent 2: ${agent2.agentId} (${agent2.hostname})`);
    
    // Verify both agents exist as separate entities
    const foundAgents = await Agent.find({
      agentId: { $in: [agent1Id, agent2Id] }
    });
    
    console.log(`\nFound ${foundAgents.length} agents:`);
    foundAgents.forEach(agent => {
      console.log(`- ${agent.agentId} (${agent.hostname}) - Status: ${agent.status}`);
    });
    
    if (foundAgents.length === 2) {
      console.log('\n✅ SUCCESS: Both agents are properly distinguished!');
      
      // Test updating one agent doesn't affect the other
      agent1.status = 'warning';
      await agent1.save();
      
      const updatedAgent1 = await Agent.findOne({ agentId: agent1Id });
      const unchangedAgent2 = await Agent.findOne({ agentId: agent2Id });
      
      console.log(`\nAfter updating agent 1 status:`);
      console.log(`- Agent 1: ${updatedAgent1.status}`);
      console.log(`- Agent 2: ${unchangedAgent2.status}`);
      
      if (updatedAgent1.status === 'warning' && unchangedAgent2.status === 'online') {
        console.log('✅ SUCCESS: Agents are independent!');
      } else {
        console.log('❌ FAILED: Agents are not independent');
      }
      
    } else {
      console.log('❌ FAILED: Expected 2 agents, found', foundAgents.length);
    }
    
    // Clean up test agents
    await Agent.deleteMany({ 
      agentId: { $in: [agent1Id, agent2Id] }
    });
    console.log('\n🧹 Cleaned up test agents');
    
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
};

const main = async () => {
  try {
    await connectDB();
    await testAgentFix();
    console.log('\nTest completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { testAgentFix };
