/**
 * Migration script to update existing agent IDs to use the new unique format
 * This script combines user ID and hostname to create unique agent IDs
 * 
 * Run this script after deploying the agent ID fix to update existing agents
 */

const mongoose = require('mongoose');
const Agent = require('../src/models/Agent');
const logger = require('../src/utils/logger');

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection failed:', error);
    process.exit(1);
  }
};

const migrateAgentIds = async () => {
  try {
    console.log('Starting agent ID migration...');
    
    // Find all agents that need migration (those with old format agent IDs)
    const agents = await Agent.find({
      agentId: { $not: { $regex: /_/ } } // Find agents without underscore (old format)
    });
    
    console.log(`Found ${agents.length} agents to migrate`);
    
    if (agents.length === 0) {
      console.log('No agents need migration');
      return;
    }
    
    const migrationResults = {
      successful: 0,
      failed: 0,
      duplicates: 0
    };
    
    for (const agent of agents) {
      try {
        const oldAgentId = agent.agentId;
        const newAgentId = `${oldAgentId}_${agent.hostname}`;
        
        // Check if an agent with the new ID already exists
        const existingAgent = await Agent.findOne({ agentId: newAgentId });
        
        if (existingAgent) {
          console.log(`Duplicate found: Agent with ID ${newAgentId} already exists. Skipping migration for ${oldAgentId}`);
          migrationResults.duplicates++;
          continue;
        }
        
        // Update the agent ID
        agent.agentId = newAgentId;
        await agent.save();
        
        console.log(`Migrated agent: ${oldAgentId} -> ${newAgentId} (${agent.hostname})`);
        migrationResults.successful++;
        
      } catch (error) {
        console.error(`Failed to migrate agent ${agent.agentId}:`, error);
        migrationResults.failed++;
      }
    }
    
    console.log('\nMigration completed:');
    console.log(`- Successful: ${migrationResults.successful}`);
    console.log(`- Failed: ${migrationResults.failed}`);
    console.log(`- Duplicates skipped: ${migrationResults.duplicates}`);
    
    // Update any log metadata that references the old agent IDs
    console.log('\nUpdating log metadata references...');
    
    const Log = require('../src/models/Log');
    
    // This is more complex as we need to match logs to their correct agents
    // For now, we'll just log that this needs to be done manually
    console.log('Note: Log metadata agentId references may need manual review');
    console.log('Consider running a separate script to update log.metadata.agentId fields');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};

const main = async () => {
  try {
    await connectDB();
    await migrateAgentIds();
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the migration if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { migrateAgentIds };
