# Simple test for Linux Agent log ingestion
Write-Host "Testing Linux Agent Log Ingestion" -ForegroundColor Green

$baseUrl = "http://localhost:5000/api/v1"
$apiKey = "test-api-key-12345"
$headers = @{
    "X-API-Key" = $apiKey
    "Content-Type" = "application/json"
}

# Test Linux logs with new source types
$linuxLogs = @{
    logs = @(
        @{
            logId = "linux_auth_test_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Auth"
            sourceType = "auth"
            osType = "linux"
            host = "ubuntu-test-01"
            logLevel = "info"
            message = "SSH login successful for user testuser"
            additionalFields = @{
                userId = "testuser"
                sourceIp = "*************"
            }
        },
        @{
            logId = "linux_kernel_test_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Kernel"
            sourceType = "kernel"
            osType = "linux"
            host = "ubuntu-test-01"
            logLevel = "warning"
            message = "USB device disconnected"
            additionalFields = @{
                devicePath = "/dev/sdb1"
            }
        }
    )
}

try {
    $bodyJson = $linuxLogs | ConvertTo-Json -Depth 10
    $response = Invoke-RestMethod -Uri "$baseUrl/logs" -Method POST -Headers $headers -Body $bodyJson
    Write-Host "SUCCESS: Logs ingested" -ForegroundColor Green
    Write-Host "Processed: $($response.data.processed)" -ForegroundColor Gray
    Write-Host "Failed: $($response.data.failed)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test filtering
Write-Host "`nTesting OS type filtering..." -ForegroundColor Cyan
try {
    $linuxOnlyUrl = "$baseUrl/logs" + "?osType=linux" + "&limit=5"
    $linuxLogs = Invoke-RestMethod -Uri $linuxOnlyUrl -Method GET -Headers $headers
    Write-Host "SUCCESS: Retrieved $($linuxLogs.data.logs.Count) Linux logs" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
