const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authenticateToken, authenticateApi<PERSON>ey, authorize } = require('../middleware/auth');
const Agent = require('../models/Agent');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/agents
 * @desc    Get all agents with filtering and pagination
 * @access  Private
 */
router.get('/',
  authenticateToken,
  authorize(['view_agents']),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['online', 'offline', 'warning', 'error']).withMessage('Invalid status'),
    query('platform').optional().isIn(['windows', 'linux', 'macos', 'other']).withMessage('Invalid platform'),
    query('search').optional().isString().trim().isLength({ max: 100 }).withMessage('Search term too long'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const {
      page = 1,
      limit = 20,
      status,
      platform,
      search,
      sortBy = 'lastHeartbeat',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = { isActive: true };

    if (status) {
      query.status = status;
    }

    if (platform) {
      query.platform = platform;
    }

    if (search) {
      query.$or = [
        { hostname: { $regex: search, $options: 'i' } },
        { agentId: { $regex: search, $options: 'i' } },
        { ipAddress: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const [agents, totalCount] = await Promise.all([
      Agent.find(query)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('notes.createdBy', 'username email')
        .lean(),
      Agent.countDocuments(query)
    ]);

    // Calculate status counts
    const statusCounts = await Agent.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    const statusSummary = {
      online: 0,
      offline: 0,
      warning: 0,
      error: 0
    };

    statusCounts.forEach(item => {
      statusSummary[item._id] = item.count;
    });

    res.json({
      status: 'success',
      data: {
        agents,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit),
          hasNextPage: skip + parseInt(limit) < totalCount,
          hasPrevPage: parseInt(page) > 1
        },
        summary: {
          total: totalCount,
          ...statusSummary
        }
      },
    });
  })
);

/**
 * @route   GET /api/v1/agents/stats
 * @desc    Get agent statistics
 * @access  Private
 */
router.get('/stats',
  authenticateToken,
  authorize(['view_agents']),
  catchAsync(async (req, res) => {
    const [
      totalAgents,
      onlineAgents,
      offlineAgents,
      warningAgents,
      errorAgents,
      platformStats,
      recentActivity
    ] = await Promise.all([
      Agent.countDocuments({ isActive: true }),
      Agent.countDocuments({ isActive: true, status: 'online' }),
      Agent.countDocuments({ isActive: true, status: 'offline' }),
      Agent.countDocuments({ isActive: true, status: 'warning' }),
      Agent.countDocuments({ isActive: true, status: 'error' }),
      Agent.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$platform', count: { $sum: 1 } } }
      ]),
      Agent.find({ isActive: true })
        .sort({ lastHeartbeat: -1 })
        .limit(10)
        .select('agentId hostname status lastHeartbeat')
        .lean()
    ]);

    const platformSummary = {};
    platformStats.forEach(item => {
      platformSummary[item._id] = item.count;
    });

    res.json({
      status: 'success',
      data: {
        summary: {
          total: totalAgents,
          online: onlineAgents,
          offline: offlineAgents,
          warning: warningAgents,
          error: errorAgents
        },
        platforms: platformSummary,
        recentActivity
      },
    });
  })
);

/**
 * @route   GET /api/v1/agents/download/windows
 * @desc    Download Windows agent installer
 * @access  Private
 */
router.get('/download/windows',
  authenticateToken,
  authorize(['view_agents']),
  catchAsync(async (req, res) => {
    const path = require('path');
    const fs = require('fs');

    // Path to the MSI file in the static directory
    const agentPath = path.join(__dirname, '../static/agents/ExLog-Agent-Windows-v2.0.0.msi');

    // Check if file exists
    if (!fs.existsSync(agentPath)) {
      throw new AppError('Agent installer not found', 404);
    }

    // Get file stats for content length
    const stats = fs.statSync(agentPath);

    // Set headers for download
    res.setHeader('Content-Disposition', 'attachment; filename="ExLog-Agent-Windows-v2.0.0.msi"');
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'no-cache');

    // Log download
    logger.info(`Agent download initiated by user ${req.user.id} (${req.user.username})`);

    // Send file
    res.sendFile(agentPath);
  })
);

/**
 * @route   GET /api/v1/agents/:id
 * @desc    Get specific agent details
 * @access  Private
 */
router.get('/:id',
  authenticateToken,
  authorize(['view_agents']),
  [
    param('id').isMongoId().withMessage('Invalid agent ID'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const agent = await Agent.findById(req.params.id)
      .populate('notes.createdBy', 'username email');

    if (!agent) {
      throw new AppError('Agent not found', 404);
    }

    res.json({
      status: 'success',
      data: { agent },
    });
  })
);

/**
 * @route   POST /api/v1/agents/register
 * @desc    Register new agent (called by agents during first startup)
 * @access  Private (API Key)
 */
router.post('/register',
  authenticateApiKey,
  [
    body('agentId').notEmpty().trim().isLength({ max: 100 }).withMessage('Agent ID is required and must be less than 100 characters'),
    body('hostname').notEmpty().trim().isLength({ max: 255 }).withMessage('Hostname is required and must be less than 255 characters'),
    body('platform').isIn(['windows', 'linux', 'macos', 'other']).withMessage('Invalid platform'),
    body('version').notEmpty().trim().isLength({ max: 50 }).withMessage('Version is required and must be less than 50 characters'),
    body('ipAddress').optional().isIP().withMessage('Invalid IP address'),
    body('metadata').optional().isObject().withMessage('Metadata must be an object'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const { agentId, hostname, platform, version, ipAddress, metadata = {} } = req.body;

    // Create unique agent ID combining provided agentId and hostname
    // This ensures agents on different systems are treated as separate even with same API key
    const uniqueAgentId = `${agentId}_${hostname}`;

    // Check if agent already exists
    let agent = await Agent.findOne({ agentId: uniqueAgentId });

    if (agent) {
      // Update existing agent
      agent.hostname = hostname;
      agent.platform = platform;
      agent.version = version;
      agent.ipAddress = ipAddress;
      agent.lastHeartbeat = new Date();
      agent.lastSeen = new Date();
      agent.metadata = { ...agent.metadata, ...metadata };
      agent.isActive = true;

      await agent.save();

      logger.info(`Agent re-registered: ${uniqueAgentId} (${hostname})`);
    } else {
      // Create new agent
      agent = new Agent({
        agentId: uniqueAgentId,
        hostname,
        platform,
        version,
        ipAddress,
        metadata,
        status: 'online'
      });

      await agent.save();

      logger.info(`New agent registered: ${uniqueAgentId} (${hostname})`);
    }

    res.status(201).json({
      status: 'success',
      message: 'Agent registered successfully',
      data: {
        agent: {
          id: agent._id,
          agentId: agent.agentId,
          hostname: agent.hostname,
          status: agent.status,
          registeredAt: agent.createdAt
        }
      },
    });
  })
);

/**
 * @route   POST /api/v1/agents/:agentId/heartbeat
 * @desc    Agent heartbeat
 * @access  Private (API Key)
 */
router.post('/:agentId/heartbeat',
  authenticateApiKey,
  [
    param('agentId').notEmpty().trim().withMessage('Agent ID is required'),
    body('metadata').optional().isObject().withMessage('Metadata must be an object'),
    body('hostname').optional().trim().isLength({ max: 255 }).withMessage('Hostname must be less than 255 characters'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const { agentId } = req.params;
    const { metadata = {}, hostname } = req.body;

    // Create unique agent ID if hostname is provided, otherwise use agentId as-is
    const uniqueAgentId = hostname ? `${agentId}_${hostname}` : agentId;

    const agent = await Agent.findOne({ agentId: uniqueAgentId });

    if (!agent) {
      throw new AppError('Agent not found. Please register the agent first.', 404);
    }

    // Update heartbeat
    await agent.updateHeartbeat(metadata);

    res.json({
      status: 'success',
      message: 'Heartbeat received',
      data: {
        agentId: agent.agentId,
        status: agent.status,
        timestamp: new Date().toISOString(),
        nextHeartbeatExpected: new Date(Date.now() + 2 * 60 * 1000).toISOString() // 2 minutes
      },
    });
  })
);

/**
 * @route   PUT /api/v1/agents/:id
 * @desc    Update agent configuration or metadata
 * @access  Private
 */
router.put('/:id',
  authenticateToken,
  authorize(['manage_agents']),
  [
    param('id').isMongoId().withMessage('Invalid agent ID'),
    body('tags').optional().isArray().withMessage('Tags must be an array'),
    body('tags.*').optional().isString().trim().isLength({ max: 50 }).withMessage('Each tag must be a string less than 50 characters'),
    body('alertsEnabled').optional().isBoolean().withMessage('Alerts enabled must be a boolean'),
    body('maintenanceMode').optional().isBoolean().withMessage('Maintenance mode must be a boolean'),
    body('maintenanceWindow').optional().isObject().withMessage('Maintenance window must be an object'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const { tags, alertsEnabled, maintenanceMode, maintenanceWindow } = req.body;

    const agent = await Agent.findById(req.params.id);

    if (!agent) {
      throw new AppError('Agent not found', 404);
    }

    // Update allowed fields
    if (tags !== undefined) agent.tags = tags;
    if (alertsEnabled !== undefined) agent.alertsEnabled = alertsEnabled;
    if (maintenanceMode !== undefined) agent.maintenanceMode = maintenanceMode;
    if (maintenanceWindow !== undefined) agent.maintenanceWindow = maintenanceWindow;

    await agent.save();

    logger.info(`Agent updated: ${agent.agentId} by user ${req.user.id}`);

    res.json({
      status: 'success',
      message: 'Agent updated successfully',
      data: { agent },
    });
  })
);

/**
 * @route   POST /api/v1/agents/:id/notes
 * @desc    Add note to agent
 * @access  Private
 */
router.post('/:id/notes',
  authenticateToken,
  authorize(['manage_agents']),
  [
    param('id').isMongoId().withMessage('Invalid agent ID'),
    body('content').notEmpty().trim().isLength({ max: 1000 }).withMessage('Note content is required and must be less than 1000 characters'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const { content } = req.body;

    const agent = await Agent.findById(req.params.id);

    if (!agent) {
      throw new AppError('Agent not found', 404);
    }

    agent.notes.push({
      content,
      createdBy: req.user.id,
    });

    await agent.save();

    res.status(201).json({
      status: 'success',
      message: 'Note added successfully',
      data: {
        note: agent.notes[agent.notes.length - 1]
      },
    });
  })
);

/**
 * @route   DELETE /api/v1/agents/:id
 * @desc    Deactivate agent (soft delete)
 * @access  Private
 */
router.delete('/:id',
  authenticateToken,
  authorize(['manage_agents']),
  [
    param('id').isMongoId().withMessage('Invalid agent ID'),
  ],
  catchAsync(async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw handleValidationError(errors);
    }

    const agent = await Agent.findById(req.params.id);

    if (!agent) {
      throw new AppError('Agent not found', 404);
    }

    agent.isActive = false;
    agent.status = 'offline';
    await agent.save();

    logger.info(`Agent deactivated: ${agent.agentId} by user ${req.user.id}`);

    res.json({
      status: 'success',
      message: 'Agent deactivated successfully',
    });
  })
);



module.exports = router;
