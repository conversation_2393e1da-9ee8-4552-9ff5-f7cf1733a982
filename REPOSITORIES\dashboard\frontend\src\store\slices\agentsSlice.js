import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks for API calls
export const fetchAgents = createAsyncThunk(
  'agents/fetchAgents',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/agents', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch agents')
    }
  }
)

export const fetchAgentStats = createAsyncThunk(
  'agents/fetchAgentStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/agents/stats')
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch agent statistics')
    }
  }
)

export const fetchAgentById = createAsyncThunk(
  'agents/fetchAgentById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(`/agents/${id}`)
      return response.data.data.agent
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch agent details')
    }
  }
)

export const updateAgent = createAsyncThunk(
  'agents/updateAgent',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/agents/${id}`, data)
      return response.data.data.agent
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update agent')
    }
  }
)

export const addAgentNote = createAsyncThunk(
  'agents/addAgentNote',
  async ({ id, content }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/agents/${id}/notes`, { content })
      return response.data.data.note
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add note')
    }
  }
)

export const deleteAgent = createAsyncThunk(
  'agents/deleteAgent',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/agents/${id}`)
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete agent')
    }
  }
)

export const downloadAgent = createAsyncThunk(
  'agents/downloadAgent',
  async (platform, { rejectWithValue }) => {
    try {
      const response = await api.get(`/agents/download/${platform}`, {
        responseType: 'blob'
      })

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      // Get filename from response headers or use default
      const contentDisposition = response.headers['content-disposition']
      let filename = `ExLog-Agent-${platform}.msi`
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }

      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      return { platform, filename }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to download agent')
    }
  }
)

const initialState = {
  agents: [],
  selectedAgent: null,
  stats: {
    summary: {
      total: 0,
      online: 0,
      offline: 0,
      warning: 0,
      error: 0
    },
    platforms: {},
    recentActivity: []
  },
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false
  },
  isLoading: false,
  error: null,
}

const agentsSlice = createSlice({
  name: 'agents',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setSelectedAgent: (state, action) => {
      state.selectedAgent = action.payload
    },
    clearSelectedAgent: (state) => {
      state.selectedAgent = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Agents
      .addCase(fetchAgents.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAgents.fulfilled, (state, action) => {
        state.isLoading = false
        state.agents = action.payload.agents
        state.pagination = action.payload.pagination
        if (action.payload.summary) {
          state.stats.summary = action.payload.summary
        }
      })
      .addCase(fetchAgents.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Fetch Agent Stats
      .addCase(fetchAgentStats.pending, (state) => {
        // Don't set loading for stats to avoid UI flicker
      })
      .addCase(fetchAgentStats.fulfilled, (state, action) => {
        state.stats = action.payload
      })
      .addCase(fetchAgentStats.rejected, (state, action) => {
        // Don't set error for stats to avoid disrupting main UI
      })

      // Fetch Agent by ID
      .addCase(fetchAgentById.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAgentById.fulfilled, (state, action) => {
        state.isLoading = false
        state.selectedAgent = action.payload
      })
      .addCase(fetchAgentById.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Update Agent
      .addCase(updateAgent.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateAgent.fulfilled, (state, action) => {
        state.isLoading = false
        const index = state.agents.findIndex(agent => agent._id === action.payload._id)
        if (index !== -1) {
          state.agents[index] = action.payload
        }
        if (state.selectedAgent && state.selectedAgent._id === action.payload._id) {
          state.selectedAgent = action.payload
        }
      })
      .addCase(updateAgent.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Add Agent Note
      .addCase(addAgentNote.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(addAgentNote.fulfilled, (state, action) => {
        state.isLoading = false
        // Note will be reflected when agents are refetched
      })
      .addCase(addAgentNote.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Delete Agent
      .addCase(deleteAgent.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(deleteAgent.fulfilled, (state, action) => {
        state.isLoading = false
        state.agents = state.agents.filter(agent => agent._id !== action.payload)
        if (state.selectedAgent && state.selectedAgent._id === action.payload) {
          state.selectedAgent = null
        }
      })
      .addCase(deleteAgent.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Download Agent
      .addCase(downloadAgent.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(downloadAgent.fulfilled, (state) => {
        state.isLoading = false
      })
      .addCase(downloadAgent.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
  },
})

export const { clearError, setSelectedAgent, clearSelectedAgent } = agentsSlice.actions
export default agentsSlice.reducer
