# Test script for Linux Agent log ingestion with new source types and OS type
# This script tests the updated dashboard functionality

Write-Host "Testing Linux Agent Log Ingestion with New Source Types" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:5000/api/v1"
$apiKey = "test-api-key-12345"

# Headers for API requests
$headers = @{
    "X-API-Key" = $apiKey
    "Content-Type" = "application/json"
}

# Function to make API requests with error handling
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [hashtable]$Headers,
        [object]$Body = $null
    )
    
    try {
        $bodyJson = if ($Body) { $Body | ConvertTo-Json -Depth 10 } else { $null }
        $response = if ($bodyJson) {
            Invoke-RestMethod -Uri $Uri -Method $Method -Headers $Headers -Body $bodyJson
        } else {
            Invoke-RestMethod -Uri $Uri -Method $Method -Headers $Headers
        }
        return $response
    } catch {
        Write-Host "API Request failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response: $responseBody" -ForegroundColor Red
        }
        return $null
    }
}

# Test 1: Linux Agent logs with new source types
Write-Host "`n1. Testing Linux Agent logs with new source types..." -ForegroundColor Cyan

$linuxLogs = @{
    logs = @(
        @{
            logId = "linux_auth_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Auth"
            sourceType = "auth"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "info"
            message = "User john logged in via SSH from *************"
            additionalFields = @{
                processId = "1234"
                userId = "john"
                sessionId = "ssh-session-001"
                sourceIp = "*************"
            }
        },
        @{
            logId = "linux_kernel_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Kernel"
            sourceType = "kernel"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "warning"
            message = "USB device disconnected: /dev/sdb1"
            additionalFields = @{
                devicePath = "/dev/sdb1"
                kernelModule = "usb-storage"
                eventType = "device_disconnect"
            }
        },
        @{
            logId = "linux_service_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Service"
            sourceType = "service"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "info"
            message = "Apache2 service started successfully"
            additionalFields = @{
                serviceName = "apache2"
                serviceAction = "start"
                exitCode = "0"
            }
        },
        @{
            logId = "linux_systemd_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Systemd"
            sourceType = "systemd"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "info"
            message = "Started Daily apt download activities"
            additionalFields = @{
                unit = "apt-daily.service"
                unitType = "service"
                systemdAction = "started"
            }
        },
        @{
            logId = "linux_hardware_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Hardware"
            sourceType = "hardware"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "error"
            message = "CPU temperature threshold exceeded: 85°C"
            additionalFields = @{
                component = "CPU"
                temperature = "85"
                threshold = "80"
                sensorId = "coretemp-isa-0000"
            }
        },
        @{
            logId = "linux_scheduler_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Scheduler"
            sourceType = "scheduler"
            osType = "linux"
            host = "ubuntu-server-01"
            logLevel = "info"
            message = "Cron job executed: /usr/bin/updatedb"
            additionalFields = @{
                cronJob = "/usr/bin/updatedb"
                cronUser = "root"
                executionTime = "2.3"
            }
        }
    )
}

$ingestResponse = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/logs" -Headers $headers -Body $linuxLogs
if ($ingestResponse) {
    Write-Host "✓ Linux logs ingested successfully" -ForegroundColor Green
    Write-Host "  Processed: $($ingestResponse.data.processed)" -ForegroundColor Gray
    Write-Host "  Failed: $($ingestResponse.data.failed)" -ForegroundColor Gray
} else {
    Write-Host "✗ Linux log ingestion failed" -ForegroundColor Red
}

# Test 2: Windows Agent logs for comparison
Write-Host "`n2. Testing Windows Agent logs for comparison..." -ForegroundColor Cyan

$windowsLogs = @{
    logs = @(
        @{
            logId = "windows_system_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "System"
            sourceType = "event"
            osType = "windows"
            host = "DESKTOP-WIN10-01"
            logLevel = "info"
            message = "Windows service started: Windows Update"
            additionalFields = @{
                eventId = "7036"
                serviceName = "wuauserv"
                serviceAction = "start"
            }
        },
        @{
            logId = "windows_security_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Security"
            sourceType = "security"
            osType = "windows"
            host = "DESKTOP-WIN10-01"
            logLevel = "warning"
            message = "User login attempt failed for user: testuser"
            additionalFields = @{
                eventId = "4625"
                userName = "testuser"
                logonType = "3"
                sourceIp = "*************"
            }
        }
    )
}

$ingestResponse2 = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/logs" -Headers $headers -Body $windowsLogs
if ($ingestResponse2) {
    Write-Host "✓ Windows logs ingested successfully" -ForegroundColor Green
    Write-Host "  Processed: $($ingestResponse2.data.processed)" -ForegroundColor Gray
    Write-Host "  Failed: $($ingestResponse2.data.failed)" -ForegroundColor Gray
} else {
    Write-Host "✗ Windows log ingestion failed" -ForegroundColor Red
}

# Wait for processing
Write-Host "`n3. Waiting 5 seconds for log processing..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Test 3: Query logs with new filters
Write-Host "`n4. Testing log retrieval with new filters..." -ForegroundColor Cyan

# Test OS type filtering
Write-Host "  Testing OS type filtering..." -ForegroundColor Gray
$linuxOnlyLogs = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/logs?osType=linux`&limit=10" -Headers $headers
if ($linuxOnlyLogs) {
    Write-Host "  ✓ Linux-only logs retrieved: $($linuxOnlyLogs.data.logs.Count)" -ForegroundColor Green
    $linuxOnlyLogs.data.logs | ForEach-Object {
        Write-Host "    - $($_.source) ($($_.osType)): $($_.message.Substring(0, [Math]::Min(50, $_.message.Length)))..." -ForegroundColor Gray
    }
} else {
    Write-Host "  ✗ Failed to retrieve Linux-only logs" -ForegroundColor Red
}

$windowsOnlyLogs = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/logs?osType=windows`&limit=10" -Headers $headers
if ($windowsOnlyLogs) {
    Write-Host "  ✓ Windows-only logs retrieved: $($windowsOnlyLogs.data.logs.Count)" -ForegroundColor Green
    $windowsOnlyLogs.data.logs | ForEach-Object {
        Write-Host "    - $($_.source) ($($_.osType)): $($_.message.Substring(0, [Math]::Min(50, $_.message.Length)))..." -ForegroundColor Gray
    }
} else {
    Write-Host "  ✗ Failed to retrieve Windows-only logs" -ForegroundColor Red
}

# Test new source type filtering
Write-Host "  Testing new source type filtering..." -ForegroundColor Gray
$authLogs = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/logs?source=Auth`&limit=5" -Headers $headers
if ($authLogs) {
    Write-Host "  ✓ Auth logs retrieved: $($authLogs.data.logs.Count)" -ForegroundColor Green
} else {
    Write-Host "  ✗ Failed to retrieve Auth logs" -ForegroundColor Red
}

$kernelLogs = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/logs?source=Kernel`&limit=5" -Headers $headers
if ($kernelLogs) {
    Write-Host "  ✓ Kernel logs retrieved: $($kernelLogs.data.logs.Count)" -ForegroundColor Green
} else {
    Write-Host "  ✗ Failed to retrieve Kernel logs" -ForegroundColor Red
}

# Test 4: Check for duplicates
Write-Host "`n5. Testing for duplicate logs..." -ForegroundColor Cyan
$allLogs = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/logs?limit=100" -Headers $headers
$noDuplicates = $false
if ($allLogs) {
    $logIds = $allLogs.data.logs | ForEach-Object { $_.logId }
    $uniqueLogIds = $logIds | Sort-Object -Unique

    if ($logIds.Count -eq $uniqueLogIds.Count) {
        Write-Host "✓ No duplicate logs found" -ForegroundColor Green
        Write-Host "  Total logs: $($logIds.Count)" -ForegroundColor Gray
        $noDuplicates = $true
    } else {
        Write-Host "✗ Duplicate logs detected!" -ForegroundColor Red
        Write-Host "  Total logs: $($logIds.Count)" -ForegroundColor Gray
        Write-Host "  Unique logs: $($uniqueLogIds.Count)" -ForegroundColor Gray

        # Find duplicates
        $duplicates = $logIds | Group-Object | Where-Object { $_.Count -gt 1 }
        Write-Host "  Duplicate log IDs:" -ForegroundColor Red
        $duplicates | ForEach-Object {
            Write-Host "    - $($_.Name) (appears $($_.Count) times)" -ForegroundColor Red
        }
        $noDuplicates = $false
    }
} else {
    Write-Host "✗ Failed to retrieve logs for duplicate check" -ForegroundColor Red
    $noDuplicates = $false
}

Write-Host "`nTest completed!" -ForegroundColor Green
Write-Host "Summary:" -ForegroundColor Yellow

$test1Status = if ($ingestResponse) { "PASSED" } else { "FAILED" }
$test1Color = if ($ingestResponse) { "Green" } else { "Red" }
Write-Host "- Linux agent logs with new source types: $test1Status" -ForegroundColor $test1Color

$test2Status = if ($ingestResponse2) { "PASSED" } else { "FAILED" }
$test2Color = if ($ingestResponse2) { "Green" } else { "Red" }
Write-Host "- Windows agent logs for comparison: $test2Status" -ForegroundColor $test2Color

$test3Status = if ($linuxOnlyLogs -and $windowsOnlyLogs) { "PASSED" } else { "FAILED" }
$test3Color = if ($linuxOnlyLogs -and $windowsOnlyLogs) { "Green" } else { "Red" }
Write-Host "- OS type filtering: $test3Status" -ForegroundColor $test3Color

$test4Status = if ($authLogs -and $kernelLogs) { "PASSED" } else { "FAILED" }
$test4Color = if ($authLogs -and $kernelLogs) { "Green" } else { "Red" }
Write-Host "- New source type filtering: $test4Status" -ForegroundColor $test4Color

$test5Status = if ($noDuplicates) { "PASSED" } else { "FAILED" }
$test5Color = if ($noDuplicates) { "Green" } else { "Red" }
Write-Host "- No duplicate logs: $test5Status" -ForegroundColor $test5Color
