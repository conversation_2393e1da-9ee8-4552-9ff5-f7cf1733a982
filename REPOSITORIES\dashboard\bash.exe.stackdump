Stack trace:
Frame         Function      Args
0007FFFF9B80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x2118E
0007FFFF9B80  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9B80  0002100469F2 (00021028DF99, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9B80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9B80  00021006A545 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF11D80000 ntdll.dll
7FFF113B0000 KERNEL32.DLL
7FFF0F5D0000 KERNELBASE.dll
7FFF0FB20000 USER32.dll
7FFF0F4F0000 win32u.dll
7FFF11710000 GDI32.dll
7FFF0F050000 gdi32full.dll
000210040000 msys-2.0.dll
7FFF0F520000 msvcp_win.dll
7FFF0F190000 ucrtbase.dll
7FFF11870000 advapi32.dll
7FFF0FDA0000 msvcrt.dll
7FFF11740000 sechost.dll
7FFF0FE50000 RPCRT4.dll
7FFF0E5F0000 CRYPTBASE.DLL
7FFF0F9C0000 bcryptPrimitives.dll
7FFF11510000 IMM32.DLL
