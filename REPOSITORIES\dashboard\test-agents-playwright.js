const { chromium } = require('playwright');

async function testAgentsPage() {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  // Listen for console messages
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️  Console ${type.toUpperCase()}: ${text}`);
  });

  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`❌ Page Error: ${error.message}`);
  });

  // Listen for network requests
  page.on('request', request => {
    if (request.url().includes('/api/')) {
      console.log(`🌐 API Request: ${request.method()} ${request.url()}`);
    }
  });

  // Listen for network responses
  page.on('response', response => {
    if (response.url().includes('/api/')) {
      console.log(`📡 API Response: ${response.status()} ${response.url()}`);
      if (response.status() >= 400) {
        console.log(`❌ API Error: ${response.status()} ${response.statusText()}`);
      }
    }
  });

  try {
    console.log('🚀 Starting Agents Page Test...\n');

    // Navigate to login page
    console.log('1️⃣ Navigating to login page...');
    await page.goto('http://192.168.2.38:8080/login');
    await page.waitForLoadState('networkidle');

    // Login
    console.log('2️⃣ Logging in...');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'Admin123!');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('   ✅ Login successful');

    // Navigate to agents page
    console.log('3️⃣ Navigating to agents page...');
    await page.goto('http://192.168.2.38:8080/agents');
    await page.waitForLoadState('networkidle');
    console.log('   ✅ Agents page loaded');

    // Wait a bit for any async operations
    await page.waitForTimeout(3000);

    // Check if the page loaded correctly
    const pageTitle = await page.textContent('h1');
    console.log(`   📄 Page title: ${pageTitle}`);

    // Check for any error messages on the page
    const errorElements = await page.locator('[role="alert"], .error, .MuiAlert-standardError').all();
    if (errorElements.length > 0) {
      console.log('⚠️  Found error elements on page:');
      for (const element of errorElements) {
        const text = await element.textContent();
        console.log(`   ❌ Error: ${text}`);
      }
    }

    // Check if statistics cards are visible
    console.log('4️⃣ Checking statistics cards...');
    const statsCards = await page.locator('[data-testid="stats-card"], .MuiCard-root').count();
    console.log(`   📊 Found ${statsCards} statistics cards`);

    // Check if download tab is available
    console.log('5️⃣ Testing download functionality...');
    const downloadTab = page.locator('text=Download');
    if (await downloadTab.isVisible()) {
      await downloadTab.click();
      await page.waitForTimeout(1000);
      console.log('   ✅ Download tab clicked');

      // Try to click download button
      const downloadButton = page.locator('button:has-text("Download")').first();
      if (await downloadButton.isVisible()) {
        console.log('   🔽 Attempting to download agent...');
        
        // Set up download handling
        const downloadPromise = page.waitForEvent('download');
        await downloadButton.click();
        
        try {
          const download = await downloadPromise;
          console.log(`   ✅ Download started: ${download.suggestedFilename()}`);
          console.log(`   📁 Download URL: ${download.url()}`);
        } catch (downloadError) {
          console.log(`   ❌ Download failed: ${downloadError.message}`);
        }
      } else {
        console.log('   ⚠️  Download button not found');
      }
    } else {
      console.log('   ⚠️  Download tab not found');
    }

    // Check agents table
    console.log('6️⃣ Checking agents table...');
    await page.locator('text=All Agents').click();
    await page.waitForTimeout(1000);
    
    const agentsTable = page.locator('table, .MuiTable-root');
    if (await agentsTable.isVisible()) {
      const rows = await page.locator('tbody tr').count();
      console.log(`   📋 Found ${rows} agent rows in table`);
    } else {
      const noAgentsMessage = await page.locator('text=No Agents Found').isVisible();
      if (noAgentsMessage) {
        console.log('   📭 No agents found (expected for new installation)');
      } else {
        console.log('   ⚠️  Agents table not found');
      }
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'agents-page-test.png', fullPage: true });
    console.log('   📸 Screenshot saved as agents-page-test.png');

    console.log('\n🎉 Agents page test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'agents-page-error.png', fullPage: true });
    console.log('   📸 Error screenshot saved as agents-page-error.png');
  } finally {
    await browser.close();
  }
}

// Run the test
testAgentsPage().catch(console.error);
