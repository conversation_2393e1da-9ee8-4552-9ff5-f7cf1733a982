[Unit]
Description=Linux Log Collection Agent
Documentation=https://github.com/your-org/linux-log-agent
After=network.target
Wants=network.target

[Service]
Type=simple
User=linux-log-agent
Group=linux-log-agent
WorkingDirectory=/opt/linux-log-agent
ExecStart=/opt/linux-log-agent/run_agent.sh service --config /etc/linux-log-agent/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=15
FinalKillSignal=SIGKILL
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=linux-log-agent

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/linux-log-agent /tmp
SupplementaryGroups=adm systemd-journal syslog

# Resource limits
LimitNOFILE=65536
MemoryMax=256M
CPUQuota=10%

[Install]
WantedBy=multi-user.target
