import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material'
import {
  Add,
  MoreVert,
  Visibility,
  FileCopy,
  Download,
  Security,
  Assessment,
  TrendingUp,
  Business,
  PlayArrow,
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import api from '../../../services/api'
import { createReport, executeReport } from '../../../store/slices/reportingSlice'

const ReportTemplates = () => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const { loading, error } = useSelector(state => state.reporting)
  
  const [templates, setTemplates] = useState([])
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [loadingTemplates, setLoadingTemplates] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [newReportData, setNewReportData] = useState({
    name: '',
    description: '',
    folder: 'My Reports'
  })

  const categories = [
    { value: 'all', label: 'All Templates', icon: <Business /> },
    { value: 'security', label: 'Security', icon: <Security /> },
    { value: 'compliance', label: 'Compliance', icon: <Assessment /> },
    { value: 'operations', label: 'Operations', icon: <TrendingUp /> },
    { value: 'executive', label: 'Executive', icon: <Business /> }
  ]

  useEffect(() => {
    loadTemplates()
  }, [selectedCategory])

  const loadTemplates = async () => {
    setLoadingTemplates(true)
    try {
      const params = selectedCategory !== 'all' ? { category: selectedCategory } : {}
      const response = await api.get('/reports/templates', { params })
      setTemplates(response.data.data.templates)
    } catch (error) {
      console.error('Failed to load templates:', error)
    } finally {
      setLoadingTemplates(false)
    }
  }

  const handleMenuOpen = (event, template) => {
    setAnchorEl(event.currentTarget)
    setSelectedTemplate(template)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedTemplate(null)
  }

  const handleCreateFromTemplate = () => {
    if (selectedTemplate) {
      setNewReportData({
        name: `${selectedTemplate.name} - Copy`,
        description: selectedTemplate.description,
        folder: 'My Reports'
      })
      setCreateDialogOpen(true)
    }
    handleMenuClose()
  }

  const handlePreviewTemplate = async () => {
    if (selectedTemplate) {
      try {
        await dispatch(executeReport({ 
          reportId: selectedTemplate._id, 
          parameters: {} 
        }))
      } catch (error) {
        console.error('Failed to preview template:', error)
      }
    }
    handleMenuClose()
  }

  const handleCreateReport = async () => {
    if (!selectedTemplate) return

    try {
      const reportData = {
        name: newReportData.name,
        description: newReportData.description,
        type: selectedTemplate.type,
        content: selectedTemplate.content,
        tags: [...(selectedTemplate.tags || []), 'from-template'],
        folder: newReportData.folder
      }

      await dispatch(createReport(reportData))
      setCreateDialogOpen(false)
      setNewReportData({ name: '', description: '', folder: 'My Reports' })
    } catch (error) {
      console.error('Failed to create report from template:', error)
    }
  }

  const getTemplateIcon = (template) => {
    if (template.tags?.includes('security')) return <Security color="error" />
    if (template.tags?.includes('compliance')) return <Assessment color="warning" />
    if (template.tags?.includes('operations')) return <TrendingUp color="info" />
    if (template.tags?.includes('executive')) return <Business color="primary" />
    return <Assessment color="action" />
  }

  const getTemplateColor = (template) => {
    if (template.tags?.includes('security')) return theme.palette.error.light
    if (template.tags?.includes('compliance')) return theme.palette.warning.light
    if (template.tags?.includes('operations')) return theme.palette.info.light
    if (template.tags?.includes('executive')) return theme.palette.primary.light
    return theme.palette.grey[200]
  }

  const renderTemplateCard = (template) => (
    <Grid item xs={12} sm={6} md={4} key={template._id}>
      <Card 
        sx={{ 
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          border: `2px solid ${getTemplateColor(template)}`,
          '&:hover': {
            boxShadow: theme.shadows[8],
            transform: 'translateY(-2px)',
            transition: 'all 0.2s ease-in-out'
          }
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {getTemplateIcon(template)}
              <Typography variant="h6" component="h3" noWrap>
                {template.name}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, template)}
            >
              <MoreVert />
            </IconButton>
          </Box>

          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ mb: 2, minHeight: 40 }}
          >
            {template.description}
          </Typography>

          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
            <Chip 
              label={template.type} 
              size="small" 
              variant="outlined"
              color="primary"
            />
            {template.tags?.slice(0, 2).map((tag) => (
              <Chip 
                key={tag} 
                label={tag} 
                size="small" 
                variant="outlined"
              />
            ))}
          </Box>

          <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
            <Button
              size="small"
              startIcon={<PlayArrow />}
              onClick={() => {
                setSelectedTemplate(template)
                handlePreviewTemplate()
              }}
              variant="outlined"
              fullWidth
            >
              Preview
            </Button>
            <Button
              size="small"
              startIcon={<Add />}
              onClick={() => {
                setSelectedTemplate(template)
                handleCreateFromTemplate()
              }}
              variant="contained"
              fullWidth
            >
              Use Template
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  )

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Report Templates
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Choose from pre-built report templates to quickly create comprehensive reports.
        </Typography>

        <Tabs
          value={selectedCategory}
          onChange={(e, value) => setSelectedCategory(value)}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {categories.map((category) => (
            <Tab
              key={category.value}
              value={category.value}
              icon={category.icon}
              label={category.label}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      {loadingTemplates ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {templates.map(renderTemplateCard)}
          {templates.length === 0 && (
            <Grid item xs={12}>
              <Alert severity="info">
                No templates found for the selected category.
              </Alert>
            </Grid>
          )}
        </Grid>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handlePreviewTemplate}>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          <ListItemText>Preview</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleCreateFromTemplate}>
          <ListItemIcon>
            <FileCopy fontSize="small" />
          </ListItemIcon>
          <ListItemText>Create Report</ListItemText>
        </MenuItem>
      </Menu>

      {/* Create Report Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create Report from Template</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Report Name"
            fullWidth
            variant="outlined"
            value={newReportData.name}
            onChange={(e) => setNewReportData({ ...newReportData, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newReportData.description}
            onChange={(e) => setNewReportData({ ...newReportData, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Folder"
            fullWidth
            variant="outlined"
            value={newReportData.folder}
            onChange={(e) => setNewReportData({ ...newReportData, folder: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateReport} 
            variant="contained"
            disabled={!newReportData.name.trim()}
          >
            Create Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ReportTemplates
