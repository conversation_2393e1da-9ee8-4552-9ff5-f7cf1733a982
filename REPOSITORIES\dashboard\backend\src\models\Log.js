const mongoose = require('mongoose');
const config = require('../config');

const logSchema = new mongoose.Schema({
  logId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  timestamp: {
    type: Date,
    required: true,
    index: true,
  },
  source: {
    type: String,
    required: true,
    index: true,
    enum: ['System', 'Application', 'Security', 'Network', 'Custom'],
  },
  sourceType: {
    type: String,
    required: true,
    index: true,
    enum: ['event', 'application', 'security', 'network', 'audit', 'performance'],
  },
  host: {
    type: String,
    required: true,
    index: true,
  },
  logLevel: {
    type: String,
    required: true,
    index: true,
    enum: ['critical', 'error', 'warning', 'info', 'debug'],
  },
  message: {
    type: String,
    required: true,
    text: true, // Enable text search
  },
  rawData: {
    type: String,
    default: null,
  },
  additionalFields: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
  metadata: {
    collectionTime: {
      type: Date,
      default: Date.now,
    },
    agentId: {
      type: String,
      index: true,
    },
    agentVersion: String,
    standardizerVersion: String,
    processingTime: Number,
    sourceMetadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },
  tags: [{
    type: String,
    index: true,
  }],
  severity: {
    type: Number,
    min: 1,
    max: 5,
    index: true,
  },
  category: {
    type: String,
    index: true,
  },
  eventId: String,
  userId: String,
  sessionId: String,
  processId: String,
  threadId: String,
  correlationId: String,
  parentLogId: String,
  childLogIds: [String],
  alertTriggered: {
    type: Boolean,
    default: false,
    index: true,
  },
  alertIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Alert',
  }],
  archived: {
    type: Boolean,
    default: false,
    index: true,
  },
  archivedAt: Date,
  retentionPolicy: {
    type: String,
    default: 'default',
  },
  checksum: String,
}, {
  timestamps: true,
});

// Compound indexes for common queries
logSchema.index({ timestamp: -1, source: 1 });
logSchema.index({ timestamp: -1, logLevel: 1 });
logSchema.index({ timestamp: -1, host: 1 });
logSchema.index({ timestamp: -1, 'metadata.agentId': 1 });
logSchema.index({ source: 1, sourceType: 1, timestamp: -1 });
logSchema.index({ logLevel: 1, timestamp: -1 });
logSchema.index({ host: 1, timestamp: -1 });
logSchema.index({ alertTriggered: 1, timestamp: -1 });
logSchema.index({ archived: 1, timestamp: -1 });

// Text index for full-text search
logSchema.index({
  message: 'text',
  'additionalFields.description': 'text',
  'additionalFields.details': 'text',
});

// TTL index for automatic deletion based on retention policy
// Only enable automatic deletion if configured to do so
if (config.retention.enableAutoDelete) {
  logSchema.index({ createdAt: 1 }, { expireAfterSeconds: config.retention.logRetentionSeconds });
} else {
  // In development, don't auto-delete logs
  logSchema.index({ createdAt: 1 });
}

// Virtual for formatted timestamp
logSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// Virtual for severity level name
logSchema.virtual('severityName').get(function() {
  const severityMap = {
    1: 'Low',
    2: 'Medium',
    3: 'High',
    4: 'Critical',
    5: 'Emergency',
  };
  return severityMap[this.severity] || 'Unknown';
});

// Method to check if log should be archived
logSchema.methods.shouldArchive = function(retentionDays = 90) {
  const archiveDate = new Date();
  archiveDate.setDate(archiveDate.getDate() - retentionDays);
  return this.timestamp < archiveDate;
};

// Method to generate checksum
logSchema.methods.generateChecksum = function() {
  const crypto = require('crypto');
  const data = JSON.stringify({
    logId: this.logId,
    timestamp: this.timestamp,
    source: this.source,
    message: this.message,
  });
  return crypto.createHash('sha256').update(data).digest('hex');
};

// Pre-save middleware
logSchema.pre('save', function(next) {
  // Generate checksum if not provided
  if (!this.checksum) {
    this.checksum = this.generateChecksum();
  }
  
  // Set severity based on log level if not provided
  if (!this.severity) {
    const severityMap = {
      'debug': 1,
      'info': 2,
      'warning': 3,
      'error': 4,
      'critical': 5,
    };
    this.severity = severityMap[this.logLevel] || 2;
  }
  
  next();
});

// Static method to get logs by time range
logSchema.statics.getByTimeRange = function(startTime, endTime, options = {}) {
  const query = {
    timestamp: {
      $gte: new Date(startTime),
      $lte: new Date(endTime),
    },
  };
  
  if (options.source) query.source = options.source;
  if (options.logLevel) query.logLevel = options.logLevel;
  if (options.host) query.host = options.host;
  if (options.agentId) query['metadata.agentId'] = options.agentId;
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(options.limit || 1000)
    .skip(options.skip || 0);
};

// Static method for full-text search
logSchema.statics.searchText = function(searchTerm, options = {}) {
  const query = {
    $text: { $search: searchTerm },
  };
  
  if (options.startTime && options.endTime) {
    query.timestamp = {
      $gte: new Date(options.startTime),
      $lte: new Date(options.endTime),
    };
  }
  
  return this.find(query, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' }, timestamp: -1 })
    .limit(options.limit || 100);
};

// Static method to get log statistics
logSchema.statics.getStatistics = async function(timeRange = '24h') {
  const now = new Date();
  let startTime;

  // Calculate time range
  switch (timeRange) {
    case '1h':
      startTime = new Date(now.getTime() - (60 * 60 * 1000));
      break;
    case '24h':
      startTime = new Date(now.getTime() - (24 * 60 * 60 * 1000));
      break;
    case '7d':
      startTime = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      break;
    case '30d':
      startTime = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      break;
    default:
      startTime = new Date(now.getTime() - (24 * 60 * 60 * 1000));
  }

  // Get comprehensive statistics
  const [
    totalLogs,
    logsByLevel,
    logsBySource,
    logsByHost,
    topEventTypes,
    recentActivity,
    criticalEvents
  ] = await Promise.all([
    // Total logs count
    this.countDocuments({ timestamp: { $gte: startTime } }),

    // Logs by level
    this.aggregate([
      { $match: { timestamp: { $gte: startTime } } },
      { $group: { _id: '$logLevel', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]),

    // Logs by source
    this.aggregate([
      { $match: { timestamp: { $gte: startTime } } },
      { $group: { _id: '$source', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]),

    // Logs by host
    this.aggregate([
      { $match: { timestamp: { $gte: startTime } } },
      { $group: { _id: '$host', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]),

    // Top event types (based on message patterns)
    this.aggregate([
      { $match: { timestamp: { $gte: startTime } } },
      {
        $group: {
          _id: {
            source: '$source',
            logLevel: '$logLevel'
          },
          count: { $sum: 1 },
          latestTimestamp: { $max: '$timestamp' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]),

    // Recent activity (hourly breakdown for charts)
    this.aggregate([
      { $match: { timestamp: { $gte: startTime } } },
      {
        $group: {
          _id: {
            hour: { $hour: '$timestamp' },
            date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1, '_id.hour': 1 } }
    ]),

    // Critical events
    this.countDocuments({
      timestamp: { $gte: startTime },
      logLevel: { $in: ['critical', 'error'] }
    })
  ]);

  return {
    totalLogs,
    logsByLevel,
    logsBySource,
    logsByHost,
    topEventTypes,
    recentActivity,
    criticalEvents,
    timeRange,
    startTime,
    endTime: now
  };
};

module.exports = mongoose.model('Log', logSchema);
