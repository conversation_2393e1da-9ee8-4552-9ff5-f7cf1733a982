/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import { Dialog } from '../api/Dialog.js';
import type { UserPrompt } from './core/UserPrompt.js';
export declare class BidiDialog extends Dialog {
    #private;
    static from(prompt: UserPrompt): BidiDialog;
    private constructor();
    handle(options: {
        accept: boolean;
        text?: string;
    }): Promise<void>;
}
//# sourceMappingURL=Dialog.d.ts.map