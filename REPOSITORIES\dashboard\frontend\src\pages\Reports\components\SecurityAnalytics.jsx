import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
} from '@mui/material'
import {
  Security,
  Warning,
  Error,
  Info,
  TrendingUp,
  TrendingDown,
  Public,
  Shield,
  Download,
  Refresh,
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import {
  fetchSecurityAnalytics,
  fetchIncidentAnalytics,
  fetchThreatIntelligence,
  fetchOperationalMetrics,
  executeReport,
  exportReportPDF,
  exportReportCSV,
} from '../../../store/slices/reportingSlice'
// import ChartComponents from './ChartComponents'

const SecurityAnalytics = ({ timeRange = '30d' }) => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const [refreshing, setRefreshing] = useState(false)
  const {
    securityAnalytics,
    incidentAnalytics,
    threatIntelligence,
    operationalMetrics,
    loading,
    error,
  } = useSelector(state => state.reporting)

  useEffect(() => {
    loadAnalyticsData()
  }, [dispatch, timeRange])

  const loadAnalyticsData = async () => {
    setRefreshing(true)
    try {
      await Promise.all([
        dispatch(fetchSecurityAnalytics(timeRange)),
        dispatch(fetchIncidentAnalytics(timeRange)),
        dispatch(fetchThreatIntelligence(timeRange)),
        dispatch(fetchOperationalMetrics(timeRange))
      ])
    } finally {
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    loadAnalyticsData()
  }

  const handleExportPDF = async () => {
    try {
      // Create a temporary report for analytics data
      const reportData = {
        name: `Security Analytics Report - ${timeRange}`,
        type: 'analytics',
        content: {
          timeRange,
          includeCharts: true
        }
      }

      // For demo purposes, we'll use a mock report ID
      // In real implementation, you'd create a report first
      await dispatch(exportReportPDF({
        reportId: 'analytics-temp',
        parameters: { timeRange }
      }))
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return theme.palette.error.main
      case 'high': return theme.palette.warning.main
      case 'medium': return theme.palette.info.main
      case 'low': return theme.palette.success.main
      default: return theme.palette.grey[500]
    }
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <Error color="error" />
      case 'high': return <Warning color="warning" />
      case 'medium': return <Info color="info" />
      case 'low': return <Shield color="success" />
      default: return <Info />
    }
  }

  const renderSecurityPosture = () => {
    if (loading.securityAnalytics) {
      return (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    if (error.securityAnalytics) {
      return (
        <Card>
          <CardContent>
            <Alert severity="error">{error.securityAnalytics}</Alert>
          </CardContent>
        </Card>
      )
    }

    if (!securityAnalytics) return null

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Security Posture Overview
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h3" color="primary" gutterBottom>
                  {securityAnalytics.securityScore}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Security Score
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={securityAnalytics.securityScore}
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={8}>
              <Typography variant="subtitle1" gutterBottom>
                Recommendations
              </Typography>
              <List dense>
                {securityAnalytics.recommendations?.map((rec, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {getSeverityIcon(rec.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={rec.title}
                      secondary={rec.description}
                    />
                  </ListItem>
                ))}
                {(!securityAnalytics.recommendations || securityAnalytics.recommendations.length === 0) && (
                  <ListItem>
                    <ListItemIcon>
                      <Shield color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="No immediate recommendations"
                      secondary="Your security posture looks good!"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderIncidentAnalytics = () => {
    if (loading.incidentAnalytics || !incidentAnalytics) {
      return (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Incident Analytics</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    const severityData = incidentAnalytics.incidentsBySeverity?.map(item => ({
      name: item._id,
      value: item.count,
      color: getSeverityColor(item._id),
    })) || []

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Incident Analytics
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Incidents by Severity
              </Typography>
              <Box sx={{ py: 2 }}>
                {severityData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">{item.name}</Typography>
                    <Typography variant="body2" fontWeight="bold" sx={{ color: item.color }}>
                      {item.value}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Response Metrics
              </Typography>
              <Box sx={{ py: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Mean Time to Detect</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round((incidentAnalytics.responseMetrics?.avgMTTD || 0) / (1000 * 60))}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Mean Time to Respond</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round((incidentAnalytics.responseMetrics?.avgMTTR || 0) / (1000 * 60))}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Resolution Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {Math.round(
                      ((incidentAnalytics.responseMetrics?.resolvedIncidents || 0) /
                      (incidentAnalytics.responseMetrics?.totalIncidents || 1)) * 100
                    )}%
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderThreatIntelligence = () => {
    if (loading.threatIntelligence || !threatIntelligence) {
      return (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Threat Intelligence</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Threat Intelligence
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Top Attack Vectors
              </Typography>
              <List dense>
                {threatIntelligence.attackVectors?.slice(0, 5).map((vector, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={vector._id || 'Unknown'}
                      secondary={`${vector.count} attempts`}
                    />
                    <Chip
                      label={vector.count}
                      size="small"
                      color="error"
                      variant="outlined"
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="No attack vectors detected"
                      secondary="System appears secure"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Geographic Attack Origins
              </Typography>
              <List dense>
                {threatIntelligence.geoAttacks?.slice(0, 5).map((geo, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Public />
                    </ListItemIcon>
                    <ListItemText
                      primary={geo.country || 'Unknown'}
                      secondary={`${geo.attackCount} attacks from ${geo.uniqueIPCount} IPs`}
                    />
                  </ListItem>
                )) || (
                  <ListItem>
                    <ListItemText
                      primary="No geographic data available"
                      secondary="Insufficient attack data"
                    />
                  </ListItem>
                )}
              </List>
            </Grid>
          </Grid>
          
          {threatIntelligence.emergingThreats?.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Emerging Threats (Last 7 Days)
              </Typography>
              <List dense>
                {threatIntelligence.emergingThreats.map((threat, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <TrendingUp color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={threat._id?.pattern || 'Unknown Pattern'}
                      secondary={`${threat.count} occurrences from ${threat._id?.source}`}
                    />
                    <Chip
                      label="New"
                      size="small"
                      color="warning"
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </CardContent>
      </Card>
    )
  }

  const renderAnalyticsOverview = () => {
    const isLoading = loading.securityAnalytics || loading.incidentAnalytics || loading.operationalMetrics

    if (isLoading) {
      return (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    // Calculate overview metrics from available data
    const totalIncidents = incidentAnalytics?.responseMetrics?.totalIncidents || 0
    const criticalIncidents = incidentAnalytics?.incidentsBySeverity?.find(s => s._id === 'critical')?.count || 0
    const securityScore = securityAnalytics?.securityScore || 0
    const activeLogSources = operationalMetrics?.sourceHealth?.length || 0

    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Security Analytics Overview
            </Typography>
            <Box>
              <Button
                startIcon={<Refresh />}
                onClick={handleRefresh}
                disabled={refreshing}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                startIcon={<Download />}
                onClick={handleExportPDF}
                variant="outlined"
                size="small"
              >
                Export PDF
              </Button>
            </Box>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 2 }}>
                <Typography variant="h4" color="primary.contrastText" gutterBottom>
                  {securityScore}
                </Typography>
                <Typography variant="body2" color="primary.contrastText">
                  Security Score
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'error.light', borderRadius: 2 }}>
                <Typography variant="h4" color="error.contrastText" gutterBottom>
                  {criticalIncidents}
                </Typography>
                <Typography variant="body2" color="error.contrastText">
                  Critical Incidents
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.light', borderRadius: 2 }}>
                <Typography variant="h4" color="warning.contrastText" gutterBottom>
                  {totalIncidents}
                </Typography>
                <Typography variant="body2" color="warning.contrastText">
                  Total Incidents
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
                <Typography variant="h4" color="success.contrastText" gutterBottom>
                  {activeLogSources}
                </Typography>
                <Typography variant="body2" color="success.contrastText">
                  Active Sources
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderLogVolumeChart = () => {
    if (!operationalMetrics?.sourceHealth) return null

    const chartData = operationalMetrics.sourceHealth.map(source => ({
      name: source._id,
      logs: source.totalLogs,
      errors: source.totalErrors,
      hosts: source.hostCount
    }))

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Log Volume by Source
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Chart data available: {chartData.length} sources
          </Typography>
        </CardContent>
      </Card>
    )
  }

  const renderIncidentTrendChart = () => {
    if (!incidentAnalytics?.incidentTrend) return null

    const chartData = incidentAnalytics.incidentTrend.map(item => ({
      name: item._id.date,
      incidents: item.count,
      severity: item._id.severity
    }))

    // Group by date and sum incidents
    const groupedData = chartData.reduce((acc, item) => {
      const existing = acc.find(d => d.name === item.name)
      if (existing) {
        existing.incidents += item.incidents
      } else {
        acc.push({ name: item.name, incidents: item.incidents })
      }
      return acc
    }, [])

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Incident Trend Over Time
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Chart data available: {groupedData.length} data points
          </Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {renderAnalyticsOverview()}
        </Grid>

        <Grid item xs={12}>
          {renderSecurityPosture()}
        </Grid>

        <Grid item xs={12} lg={6}>
          {renderLogVolumeChart()}
        </Grid>

        <Grid item xs={12} lg={6}>
          {renderIncidentTrendChart()}
        </Grid>

        <Grid item xs={12} lg={6}>
          {renderIncidentAnalytics()}
        </Grid>

        <Grid item xs={12} lg={6}>
          {renderThreatIntelligence()}
        </Grid>
      </Grid>
    </Box>
  )
}

export default SecurityAnalytics
