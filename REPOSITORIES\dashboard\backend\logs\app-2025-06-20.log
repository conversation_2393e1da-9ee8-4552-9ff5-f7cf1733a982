{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-20 17:53:30:5330"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 0 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 0 rules\u001b[39m","timestamp":"2025-06-20 17:53:36:5336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCreated system user for default rules\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing default alert rules...\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault rules initialization complete. Created: 8, Skipped: 0\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault alert rules initialized\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert system initialization completed\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mStarting agent tracking service\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-20 17:53:37:5337"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert rules changed, reloading...\u001b[39m","timestamp":"2025-06-20 17:54:06:546"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-20 17:54:06:546"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: **********\u001b[39m","timestamp":"2025-06-20 17:54:29:5429"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew API key created: test-linux-agent\u001b[39m","timestamp":"2025-06-20 18:00:51:051","userId":"6855a01e251194f63b69e328"}
{"_message":"User validation failed","errors":{"apiKeys.0.permissions.0":{"kind":"enum","message":"`ingest_logs` is not a valid enum value for path `permissions.0`.","name":"ValidatorError","path":"permissions.0","properties":{"enumValues":["view_logs","search_logs","export_logs","view_alerts","manage_alerts","view_agents","manage_agents","view_reports","generate_reports"],"message":"`ingest_logs` is not a valid enum value for path `permissions.0`.","path":"permissions.0","type":"enum","value":"ingest_logs"},"value":"ingest_logs"}},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mAPI key authentication error: User validation failed: apiKeys.0.permissions.0: `ingest_logs` is not a valid enum value for path `permissions.0`.\u001b[39m","stack":"ValidationError: User validation failed: apiKeys.0.permissions.0: `ingest_logs` is not a valid enum value for path `permissions.0`.\n    at Document.invalidate (/app/node_modules/mongoose/lib/document.js:3343:32)\n    at Subdocument.invalidate (/app/node_modules/mongoose/lib/types/subdocument.js:229:12)\n    at /app/node_modules/mongoose/lib/document.js:3104:17\n    at /app/node_modules/mongoose/lib/schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-06-20 18:29:55:2955"}
{"ip":"************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:202:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:31:57:3157","url":"/api/v1/logs","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-CA) WindowsPowerShell/5.1.19041.5965","userId":"6855a02042f295c34575d31d"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-20 18:32:57:3257"}
