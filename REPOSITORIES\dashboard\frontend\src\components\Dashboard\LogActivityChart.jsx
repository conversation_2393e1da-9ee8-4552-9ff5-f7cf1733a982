import React from 'react'
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  // ResponsiveContainer,
} from 'recharts'
import { Box, Typography, useTheme } from '@mui/material'

const LogActivityChart = ({ data, isLoading }) => {
  const theme = useTheme()

  // Transform the data for the chart
  const chartData = React.useMemo(() => {
    if (!data || !data.recentActivity) return []
    
    return data.recentActivity.map(item => ({
      time: `${item._id.hour}:00`,
      date: item._id.date,
      count: item.count,
      displayTime: `${item._id.date} ${item._id.hour}:00`
    }))
  }, [data])

  if (isLoading) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography color="text.secondary">Loading chart data...</Typography>
      </Box>
    )
  }

  if (!chartData.length) {
    return (
      <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography color="text.secondary">No activity data available</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ height: 300 }}>
      <Box sx={{ width: '100%', height: '100%' }}>
        <LineChart data={chartData} width={800} height={300}>
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="time"
            stroke={theme.palette.text.secondary}
            fontSize={12}
          />
          <YAxis
            stroke={theme.palette.text.secondary}
            fontSize={12}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: theme.shape.borderRadius,
            }}
            labelFormatter={(value, payload) => {
              if (payload && payload[0]) {
                return `Time: ${payload[0].payload.displayTime}`
              }
              return `Time: ${value}`
            }}
            formatter={(value) => [value, 'Log Count']}
          />
          <Line
            type="monotone"
            dataKey="count"
            stroke={theme.palette.primary.main}
            strokeWidth={2}
            dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: theme.palette.primary.main, strokeWidth: 2 }}
          />
        </LineChart>
      </Box>
    </Box>
  )
}

export default LogActivityChart
